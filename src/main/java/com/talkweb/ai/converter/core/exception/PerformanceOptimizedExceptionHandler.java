package com.talkweb.ai.converter.core.exception;

import com.talkweb.ai.converter.core.ConversionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能优化的异常处理器
 * 
 * 提供统一的异常处理、重试机制和性能监控功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
public class PerformanceOptimizedExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(PerformanceOptimizedExceptionHandler.class);
    
    // 重试配置
    private static final int DEFAULT_MAX_RETRIES = 3;
    private static final Duration DEFAULT_RETRY_DELAY = Duration.ofSeconds(1);
    private static final Duration MAX_RETRY_DELAY = Duration.ofSeconds(30);
    
    // 异常统计
    private static final Map<String, AtomicInteger> exceptionCounts = new ConcurrentHashMap<>();
    private static final Map<String, AtomicLong> exceptionProcessingTimes = new ConcurrentHashMap<>();
    private static final AtomicInteger totalExceptions = new AtomicInteger(0);
    private static final AtomicInteger recoveredExceptions = new AtomicInteger(0);
    
    /**
     * 带重试的操作执行
     */
    public static <T> T executeWithRetry(RetryableOperation<T> operation, String operationName) 
            throws ConversionException {
        return executeWithRetry(operation, operationName, DEFAULT_MAX_RETRIES, DEFAULT_RETRY_DELAY);
    }
    
    /**
     * 带自定义重试配置的操作执行
     */
    public static <T> T executeWithRetry(RetryableOperation<T> operation, String operationName, 
                                       int maxRetries, Duration retryDelay) throws ConversionException {
        StopWatch stopWatch = new StopWatch(operationName);
        stopWatch.start();
        
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                T result = operation.execute();
                
                stopWatch.stop();
                
                // 记录成功恢复的异常
                if (attempt > 1) {
                    recoveredExceptions.incrementAndGet();
                    log.info("操作 '{}' 在第 {} 次尝试后成功恢复，耗时: {} ms", 
                            operationName, attempt, stopWatch.getTotalTimeMillis());
                }
                
                return result;
                
            } catch (Exception e) {
                lastException = e;
                totalExceptions.incrementAndGet();
                
                // 记录异常统计
                String exceptionType = e.getClass().getSimpleName();
                exceptionCounts.computeIfAbsent(exceptionType, k -> new AtomicInteger(0)).incrementAndGet();
                
                log.warn("操作 '{}' 第 {} 次尝试失败: {}", operationName, attempt, e.getMessage());
                
                // 如果还有重试机会，等待后重试
                if (attempt <= maxRetries) {
                    try {
                        Duration actualDelay = calculateBackoffDelay(retryDelay, attempt);
                        log.debug("等待 {} ms 后重试...", actualDelay.toMillis());
                        Thread.sleep(actualDelay.toMillis());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new ConversionException("操作被中断: " + operationName, ie);
                    }
                }
            }
        }
        
        stopWatch.stop();
        
        // 记录最终失败的处理时间
        exceptionProcessingTimes.computeIfAbsent(operationName, k -> new AtomicLong(0))
                .addAndGet(stopWatch.getTotalTimeMillis());
        
        String errorMessage = String.format("操作 '%s' 在 %d 次尝试后仍然失败，总耗时: %d ms", 
                operationName, maxRetries + 1, stopWatch.getTotalTimeMillis());
        
        log.error(errorMessage, lastException);
        throw new ConversionException(errorMessage, lastException);
    }
    
    /**
     * 计算指数退避延迟
     */
    private static Duration calculateBackoffDelay(Duration baseDelay, int attempt) {
        long delayMs = baseDelay.toMillis() * (long) Math.pow(2, attempt - 1);
        return Duration.ofMillis(Math.min(delayMs, MAX_RETRY_DELAY.toMillis()));
    }
    
    /**
     * 安全执行操作（捕获所有异常）
     */
    public static <T> T executeSafely(SafeOperation<T> operation, String operationName, T defaultValue) {
        try {
            return operation.execute();
        } catch (Exception e) {
            log.warn("安全操作 '{}' 失败，使用默认值: {}", operationName, e.getMessage());
            recordException(e, operationName);
            return defaultValue;
        }
    }
    
    /**
     * 记录异常统计信息
     */
    private static void recordException(Exception e, String operationName) {
        totalExceptions.incrementAndGet();
        String exceptionType = e.getClass().getSimpleName();
        exceptionCounts.computeIfAbsent(exceptionType, k -> new AtomicInteger(0)).incrementAndGet();
    }
    
    /**
     * 获取异常统计信息
     */
    public static ExceptionStatistics getStatistics() {
        ExceptionStatistics stats = new ExceptionStatistics();
        stats.setTotalExceptions(totalExceptions.get());
        stats.setRecoveredExceptions(recoveredExceptions.get());
        stats.setExceptionCounts(exceptionCounts);
        stats.setProcessingTimes(exceptionProcessingTimes);
        stats.setSuccessRate(calculateSuccessRate());
        stats.setLastResetTime(LocalDateTime.now()); // 简化实现
        return stats;
    }
    
    /**
     * 计算成功率
     */
    private static double calculateSuccessRate() {
        int total = totalExceptions.get();
        int recovered = recoveredExceptions.get();
        
        if (total == 0) {
            return 100.0;
        }
        
        return ((double) recovered / total) * 100.0;
    }
    
    /**
     * 重置统计信息
     */
    public static void resetStatistics() {
        totalExceptions.set(0);
        recoveredExceptions.set(0);
        exceptionCounts.clear();
        exceptionProcessingTimes.clear();
        log.info("异常统计信息已重置");
    }
    
    /**
     * 可重试操作接口
     */
    @FunctionalInterface
    public interface RetryableOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 安全操作接口
     */
    @FunctionalInterface
    public interface SafeOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 异常统计信息
     */
    public static class ExceptionStatistics {
        private int totalExceptions;
        private int recoveredExceptions;
        private Map<String, AtomicInteger> exceptionCounts;
        private Map<String, AtomicLong> processingTimes;
        private double successRate;
        private LocalDateTime lastResetTime;
        
        // Getters and Setters
        public int getTotalExceptions() { return totalExceptions; }
        public void setTotalExceptions(int totalExceptions) { this.totalExceptions = totalExceptions; }
        
        public int getRecoveredExceptions() { return recoveredExceptions; }
        public void setRecoveredExceptions(int recoveredExceptions) { this.recoveredExceptions = recoveredExceptions; }
        
        public Map<String, AtomicInteger> getExceptionCounts() { return exceptionCounts; }
        public void setExceptionCounts(Map<String, AtomicInteger> exceptionCounts) { this.exceptionCounts = exceptionCounts; }
        
        public Map<String, AtomicLong> getProcessingTimes() { return processingTimes; }
        public void setProcessingTimes(Map<String, AtomicLong> processingTimes) { this.processingTimes = processingTimes; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
        
        public LocalDateTime getLastResetTime() { return lastResetTime; }
        public void setLastResetTime(LocalDateTime lastResetTime) { this.lastResetTime = lastResetTime; }
        
        @Override
        public String toString() {
            return String.format("ExceptionStatistics{totalExceptions=%d, recoveredExceptions=%d, successRate=%.2f%%}", 
                    totalExceptions, recoveredExceptions, successRate);
        }
    }
}