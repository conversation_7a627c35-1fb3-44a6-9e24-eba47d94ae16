

package com.talkweb.ai.converter.core.impl;

import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.ConversionException;
import com.talkweb.ai.converter.core.converter.AbstractDocumentConverter;
import com.talkweb.ai.converter.core.converter.ConversionCapabilities;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.ConversionMetadata;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Enhanced Excel to Markdown converter with comprehensive compatibility and structure preservation
 *
 * Features:
 * - Enhanced compatibility with different Excel formats (.xls, .xlsx, .xlsm)
 * - Complete preservation of document structure and hierarchy
 * - Advanced cell content extraction with formula and style support
 * - Merged cell detection and handling
 * - Performance optimizations with caching
 * - Robust error handling and recovery
 *
 * <AUTHOR> Assistant
 * @version 3.0 (Refactored to use new architecture)
 */
public class ExcelToMarkdownConverter extends AbstractDocumentConverter {

    // Enhanced cache with TTL and automatic cleanup
    private static final Map<String, CacheEntry> CELL_CONTENT_CACHE = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;
    private static final Duration CACHE_TTL = Duration.ofMinutes(30);
    private static volatile LocalDateTime lastCleanup = LocalDateTime.now();
    
    // Cache entry with timestamp for TTL management
    private static class CacheEntry {
        final String value;
        final LocalDateTime timestamp;
        
        CacheEntry(String value) {
            this.value = value;
            this.timestamp = LocalDateTime.now();
        }
        
        boolean isExpired() {
            return Duration.between(timestamp, LocalDateTime.now()).compareTo(CACHE_TTL) > 0;
        }
    }

    // Date formatters for different date types
    private static final SimpleDateFormat DEFAULT_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm:ss");

    // Excel file type detection
    private enum ExcelType {
        XLS,    // Excel 97-2003 format
        XLSX,   // Excel 2007+ format
        XLSM,   // Excel 2007+ with macros
        UNKNOWN
    }

    // Cell content types for enhanced processing
    private enum CellContentType {
        TEXT,
        NUMBER,
        DATE,
        DATETIME,
        TIME,
        BOOLEAN,
        FORMULA,
        ERROR,
        BLANK
    }

    /**
     * Default constructor
     */
    public ExcelToMarkdownConverter() {
        super();
    }

    @Override
    protected ConversionMetadata createMetadata() {
        return ConversionMetadata.builder("Excel to Markdown Converter")
                .description("Converts Excel files (.xls, .xlsx, .xlsm) to Markdown format with structure preservation")
                .version("3.0")
                .attribute("author", "AI Assistant")
                .attribute("supportedInputFormats", Set.of("xls", "xlsx", "xlsm"))
                .attribute("supportedOutputFormats", Set.of("md"))
                .build();
    }

    @Override
    public Set<String> getSupportedExtensions() {
        return Set.of("xls", "xlsx", "xlsm");
    }

    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.TABLES)
                .feature(ConversionCapabilities.Features.METADATA)
                .capability("batchProcessing", true)
                .capability("streaming", false)
                .capability("maxFileSize", 100 * 1024 * 1024) // 100MB
                .build();
    }

    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) throws ConversionException {
        String parentDir = inputFile.getParent();
        if (parentDir == null) {
            parentDir = "."; // Use current directory if parent is null
        }
        Path outputPath = Path.of(parentDir, inputFile.getName() + ".md");

        try {
            // Detect Excel file type for compatibility handling
            ExcelType excelType = detectExcelType(inputFile);

            // Create workbook with enhanced error handling
            try (Workbook workbook = createWorkbookSafely(inputFile, excelType)) {
                StringBuilder markdown = new StringBuilder();
                FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

                // Add document metadata if enabled in context
                if (context.getOptions().getOption("includeMetadata", true)) {
                    addDocumentMetadata(markdown, workbook, inputFile);
                }

                // Process each sheet with enhanced structure preservation
                for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                    Sheet sheet = workbook.getSheetAt(i);

                    // Skip hidden sheets unless specifically requested
                    if (workbook.isSheetHidden(i) && !context.getOptions().getOption("includeHiddenSheets", false)) {
                        continue;
                    }

                    markdown.append("## ").append(escapeMarkdownText(sheet.getSheetName())).append("\n\n");

                    // Add sheet metadata if available and enabled
                    if (context.getOptions().getOption("includeSheetMetadata", true)) {
                        addSheetMetadata(markdown, sheet);
                    }

                    // Convert sheet content with enhanced processing
                    String sheetContent = convertSheetToMarkdownEnhanced(sheet, evaluator);
                    if (!sheetContent.trim().isEmpty()) {
                        markdown.append(sheetContent);
                        markdown.append("\n\n");
                    }
                }

                return new ConversionResult(
                    ConversionResult.Status.SUCCESS,
                    inputFile.getPath(),
                    outputPath.toString(),
                    markdown.toString()
                );
            }
        } catch (Exception e) {
            throw new ConversionException("Excel转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * Enhanced sheet to markdown conversion with comprehensive structure preservation
     *
     * @param sheet the Excel sheet to convert
     * @param evaluator the formula evaluator
     * @return the markdown representation of the sheet
     */
    private String convertSheetToMarkdownEnhanced(Sheet sheet, FormulaEvaluator evaluator) {
        if (sheet == null) {
            return "";
        }

        // Analyze sheet structure
        SheetStructure structure = analyzeSheetStructure(sheet);
        if (structure.isEmpty()) {
            return "";
        }

        StringBuilder markdown = new StringBuilder();

        // Extract and process table data with merged cell handling
        List<List<String>> tableData = extractTableDataWithMergedCells(sheet, evaluator, structure);
        if (tableData.isEmpty()) {
            return "";
        }

        // Determine the maximum number of columns
        int maxCols = tableData.stream().mapToInt(List::size).max().orElse(0);
        if (maxCols == 0) {
            return "";
        }

        // Normalize table data (ensure all rows have same number of columns)
        normalizeTableData(tableData, maxCols);

        // Write enhanced table with proper formatting
        writeEnhancedTable(tableData, maxCols, markdown, structure);

        return markdown.toString();
    }

    /**
     * Legacy method for backward compatibility
     */
    private String convertSheetToMarkdown(Sheet sheet, FormulaEvaluator evaluator) {
        return convertSheetToMarkdownEnhanced(sheet, evaluator);
    }

    // ========== Enhanced Processing Methods ==========

    /**
     * Detects the Excel file type for compatibility handling
     *
     * @param file the Excel file
     * @return the detected Excel type
     */
    private ExcelType detectExcelType(File file) {
        if (file == null || !file.exists()) {
            return ExcelType.UNKNOWN;
        }

        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".xlsx")) {
            return ExcelType.XLSX;
        } else if (fileName.endsWith(".xls")) {
            return ExcelType.XLS;
        } else if (fileName.endsWith(".xlsm")) {
            return ExcelType.XLSM;
        }

        return ExcelType.UNKNOWN;
    }

    /**
     * Creates a workbook safely with enhanced error handling
     *
     * @param file the Excel file
     * @param excelType the detected Excel type
     * @return the workbook instance
     * @throws IOException if file cannot be read
     */
    private Workbook createWorkbookSafely(File file, ExcelType excelType) throws IOException {
        try {
            return WorkbookFactory.create(file);
        } catch (Exception e) {
            // Fallback to specific workbook types if generic creation fails
            try (FileInputStream fis = new FileInputStream(file)) {
                switch (excelType) {
                    case XLS:
                        return new HSSFWorkbook(fis);
                    case XLSX:
                    case XLSM:
                        return new XSSFWorkbook(fis);
                    default:
                        throw new IOException("无法识别的Excel文件格式: " + file.getName(), e);
                }
            }
        }
    }

    /**
     * Adds document metadata to the markdown output
     *
     * @param markdown the markdown builder
     * @param workbook the Excel workbook
     * @param file the source file
     */
    private void addDocumentMetadata(StringBuilder markdown, Workbook workbook, File file) {
        markdown.append("# ").append(escapeMarkdownText(file.getName())).append("\n\n");

        // Add basic file information
        markdown.append("**文件信息:**\n");
        markdown.append("- 文件名: ").append(escapeMarkdownText(file.getName())).append("\n");
        markdown.append("- 工作表数量: ").append(workbook.getNumberOfSheets()).append("\n");
        markdown.append("- 文件大小: ").append(formatFileSize(file.length())).append("\n");

        // Add workbook properties if available
        try {
            if (workbook.getCreationHelper() != null) {
                markdown.append("- 创建时间: ").append(new Date(file.lastModified())).append("\n");
            }
        } catch (Exception e) {
            // Ignore if properties are not available
        }

        markdown.append("\n");
    }

    /**
     * Adds sheet metadata to the markdown output
     *
     * @param markdown the markdown builder
     * @param sheet the Excel sheet
     */
    private void addSheetMetadata(StringBuilder markdown, Sheet sheet) {
        if (sheet == null) {
            return;
        }

        // Add sheet information if available
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        int totalRows = lastRowNum - firstRowNum + 1;

        if (totalRows > 0) {
            markdown.append("*工作表信息: ").append(totalRows).append(" 行*\n\n");
        }
    }

    /**
     * Escapes special markdown characters in text
     *
     * @param text the text to escape
     * @return the escaped text
     */
    private String escapeMarkdownText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        return text.replace("|", "\\|")
                  .replace("*", "\\*")
                  .replace("_", "\\_")
                  .replace("#", "\\#")
                  .replace("[", "\\[")
                  .replace("]", "\\]")
                  .replace("(", "\\(")
                  .replace(")", "\\)")
                  .replace("`", "\\`")
                  .replace("\\", "\\\\");
    }

    /**
     * Formats file size in human-readable format
     *
     * @param size the file size in bytes
     * @return the formatted size string
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * Sheet structure analysis result
     */
    private static class SheetStructure {
        private final int firstRow;
        private final int lastRow;
        private final int firstCol;
        private final int lastCol;
        private final Set<CellRangeAddress> mergedRegions;
        private final boolean hasHeader;

        public SheetStructure(int firstRow, int lastRow, int firstCol, int lastCol,
                            Set<CellRangeAddress> mergedRegions, boolean hasHeader) {
            this.firstRow = firstRow;
            this.lastRow = lastRow;
            this.firstCol = firstCol;
            this.lastCol = lastCol;
            this.mergedRegions = mergedRegions != null ? mergedRegions : new HashSet<>();
            this.hasHeader = hasHeader;
        }

        public boolean isEmpty() {
            return lastRow < firstRow || lastCol < firstCol;
        }

        public int getFirstRow() { return firstRow; }
        public int getLastRow() { return lastRow; }
        public int getFirstCol() { return firstCol; }
        public int getLastCol() { return lastCol; }
        public Set<CellRangeAddress> getMergedRegions() { return mergedRegions; }
        public boolean hasHeader() { return hasHeader; }
    }

    /**
     * Analyzes the structure of an Excel sheet
     *
     * @param sheet the Excel sheet to analyze
     * @return the sheet structure information
     */
    private SheetStructure analyzeSheetStructure(Sheet sheet) {
        if (sheet == null) {
            return new SheetStructure(-1, -1, -1, -1, null, false);
        }

        int firstRow = sheet.getFirstRowNum();
        int lastRow = sheet.getLastRowNum();

        if (firstRow == -1 || lastRow == -1) {
            return new SheetStructure(-1, -1, -1, -1, null, false);
        }

        // Find the actual data range
        int firstCol = Integer.MAX_VALUE;
        int lastCol = -1;

        for (int rowNum = firstRow; rowNum <= lastRow; rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row != null) {
                int rowFirstCol = row.getFirstCellNum();
                int rowLastCol = row.getLastCellNum() - 1;

                if (rowFirstCol >= 0) {
                    firstCol = Math.min(firstCol, rowFirstCol);
                    lastCol = Math.max(lastCol, rowLastCol);
                }
            }
        }

        if (firstCol == Integer.MAX_VALUE) {
            firstCol = -1;
        }

        // Extract merged regions
        Set<CellRangeAddress> mergedRegions = new HashSet<>();
        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
            mergedRegions.add(sheet.getMergedRegion(i));
        }

        // Determine if first row is header (simple heuristic)
        boolean hasHeader = determineIfHasHeader(sheet, firstRow, firstCol, lastCol);

        return new SheetStructure(firstRow, lastRow, firstCol, lastCol, mergedRegions, hasHeader);
    }

    /**
     * Determines if the first row contains headers
     *
     * @param sheet the Excel sheet
     * @param firstRow the first row number
     * @param firstCol the first column number
     * @param lastCol the last column number
     * @return true if the first row appears to be a header
     */
    private boolean determineIfHasHeader(Sheet sheet, int firstRow, int firstCol, int lastCol) {
        Row firstRowObj = sheet.getRow(firstRow);
        if (firstRowObj == null) {
            return false;
        }

        // Simple heuristic: if first row contains mostly text and subsequent rows contain numbers/dates
        int textCells = 0;
        int totalCells = 0;

        for (int colNum = firstCol; colNum <= lastCol; colNum++) {
            Cell cell = firstRowObj.getCell(colNum);
            if (cell != null) {
                totalCells++;
                if (cell.getCellType() == CellType.STRING ||
                    (cell.getCellType() == CellType.FORMULA &&
                     cell.getCachedFormulaResultType() == CellType.STRING)) {
                    textCells++;
                }
            }
        }

        return totalCells > 0 && (textCells * 1.0 / totalCells) > 0.5;
    }

    /**
     * Extracts table data with merged cell handling
     *
     * @param sheet the Excel sheet
     * @param evaluator the formula evaluator
     * @param structure the sheet structure
     * @return the extracted table data
     */
    private List<List<String>> extractTableDataWithMergedCells(Sheet sheet, FormulaEvaluator evaluator,
                                                              SheetStructure structure) {
        List<List<String>> tableData = new ArrayList<>();

        if (structure.isEmpty()) {
            return tableData;
        }

        // Create a map to track merged cell values
        Map<String, String> mergedCellValues = new HashMap<>();
        for (CellRangeAddress mergedRegion : structure.getMergedRegions()) {
            Row topLeftRow = sheet.getRow(mergedRegion.getFirstRow());
            if (topLeftRow != null) {
                Cell topLeftCell = topLeftRow.getCell(mergedRegion.getFirstColumn());
                if (topLeftCell != null) {
                    String value = extractCellContentEnhanced(topLeftCell, evaluator);

                    // Apply this value to all cells in the merged region
                    for (int row = mergedRegion.getFirstRow(); row <= mergedRegion.getLastRow(); row++) {
                        for (int col = mergedRegion.getFirstColumn(); col <= mergedRegion.getLastColumn(); col++) {
                            mergedCellValues.put(row + "," + col, value);
                        }
                    }
                }
            }
        }

        // Extract data row by row
        for (int rowNum = structure.getFirstRow(); rowNum <= structure.getLastRow(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            List<String> rowData = new ArrayList<>();

            for (int colNum = structure.getFirstCol(); colNum <= structure.getLastCol(); colNum++) {
                String cellValue = "";

                // Check if this cell is part of a merged region
                String cellKey = rowNum + "," + colNum;
                if (mergedCellValues.containsKey(cellKey)) {
                    cellValue = mergedCellValues.get(cellKey);
                } else if (row != null) {
                    Cell cell = row.getCell(colNum);
                    if (cell != null) {
                        cellValue = extractCellContentEnhanced(cell, evaluator);
                    }
                }

                rowData.add(cellValue);
            }

            tableData.add(rowData);
        }

        return tableData;
    }

    /**
     * Enhanced cell content extraction with comprehensive type handling
     *
     * @param cell the Excel cell
     * @param evaluator the formula evaluator
     * @return the extracted and formatted cell content
     */
    private String extractCellContentEnhanced(Cell cell, FormulaEvaluator evaluator) {
        if (cell == null) {
            return "";
        }

        // Check cache first for performance
        String cacheKey = generateCellCacheKey(cell);
        CacheEntry cachedEntry = CELL_CONTENT_CACHE.get(cacheKey);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            return cachedEntry.value;
        }

        String content = "";
        CellContentType contentType = determineCellContentType(cell);

        try {
            switch (contentType) {
                case TEXT:
                    content = cell.getStringCellValue();
                    break;
                case NUMBER:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        Date date = cell.getDateCellValue();
                        content = formatDateValue(date, cell);
                    } else {
                        double numValue = cell.getNumericCellValue();
                        content = formatNumericValue(numValue);
                    }
                    break;
                case BOOLEAN:
                    content = String.valueOf(cell.getBooleanCellValue());
                    break;
                case FORMULA:
                    content = extractFormulaContent(cell, evaluator);
                    break;
                case ERROR:
                    content = "ERROR: " + cell.getErrorCellValue();
                    break;
                case BLANK:
                default:
                    content = "";
                    break;
            }
        } catch (Exception e) {
            // Fallback to string representation
            content = cell.toString();
        }

        // Post-process content
        content = postProcessCellContent(content);

        // Cache the result with TTL and automatic cleanup
        cacheValueWithCleanup(cacheKey, content);

        return content;
    }

    /**
     * Generates a cache key for a cell
     *
     * @param cell the Excel cell
     * @return the cache key
     */
    private String generateCellCacheKey(Cell cell) {
        return cell.getSheet().getSheetName() + ":" +
               cell.getRowIndex() + ":" +
               cell.getColumnIndex() + ":" +
               cell.getCellType().name();
    }

    /**
     * Determines the content type of a cell
     *
     * @param cell the Excel cell
     * @return the cell content type
     */
    private CellContentType determineCellContentType(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return CellContentType.TEXT;
            case NUMERIC:
                return DateUtil.isCellDateFormatted(cell) ? CellContentType.DATE : CellContentType.NUMBER;
            case BOOLEAN:
                return CellContentType.BOOLEAN;
            case FORMULA:
                return CellContentType.FORMULA;
            case ERROR:
                return CellContentType.ERROR;
            case BLANK:
            default:
                return CellContentType.BLANK;
        }
    }

    /**
     * Formats a date value based on cell formatting
     *
     * @param date the date value
     * @param cell the Excel cell (for format information)
     * @return the formatted date string
     */
    private String formatDateValue(Date date, Cell cell) {
        if (date == null) {
            return "";
        }

        // Try to determine the appropriate format based on cell format
        String formatString = cell.getCellStyle().getDataFormatString();

        if (formatString.contains("h") || formatString.contains("H")) {
            // Contains time information
            if (formatString.contains("d") || formatString.contains("m") || formatString.contains("y")) {
                return DATETIME_FORMAT.format(date);
            } else {
                return TIME_FORMAT.format(date);
            }
        } else {
            return DEFAULT_DATE_FORMAT.format(date);
        }
    }

    /**
     * Formats a numeric value appropriately
     *
     * @param value the numeric value
     * @return the formatted numeric string
     */
    private String formatNumericValue(double value) {
        // Check if it's a whole number
        if (value == Math.floor(value) && !Double.isInfinite(value)) {
            return String.valueOf((long) value);
        } else {
            // Format with appropriate decimal places
            return String.format("%.2f", value);
        }
    }

    /**
     * Extracts content from formula cells
     *
     * @param cell the formula cell
     * @param evaluator the formula evaluator
     * @return the formula content and result
     */
    private String extractFormulaContent(Cell cell, FormulaEvaluator evaluator) {
        try {
            // Get the formula
            String formula = cell.getCellFormula();

            // Try to evaluate the formula
            CellValue cellValue = evaluator.evaluate(cell);
            String result = "";

            if (cellValue != null) {
                switch (cellValue.getCellType()) {
                    case STRING:
                        result = cellValue.getStringValue();
                        break;
                    case NUMERIC:
                        if (DateUtil.isCellDateFormatted(cell)) {
                            Date date = DateUtil.getJavaDate(cellValue.getNumberValue());
                            result = formatDateValue(date, cell);
                        } else {
                            result = formatNumericValue(cellValue.getNumberValue());
                        }
                        break;
                    case BOOLEAN:
                        result = String.valueOf(cellValue.getBooleanValue());
                        break;
                    case ERROR:
                        result = "ERROR";
                        break;
                    default:
                        result = "";
                        break;
                }
            }

            // Return result, optionally with formula
            return result.isEmpty() ? ("=" + formula) : result;

        } catch (Exception e) {
            // If evaluation fails, return the formula
            try {
                return "=" + cell.getCellFormula();
            } catch (Exception e2) {
                return "FORMULA_ERROR";
            }
        }
    }

    /**
     * Post-processes cell content for markdown compatibility
     *
     * @param content the raw cell content
     * @return the processed content
     */
    private String postProcessCellContent(String content) {
        if (content == null) {
            return "";
        }

        // Escape pipe characters and other markdown special characters
        content = content.replace("|", "\\|")
                        .replace("\n", " ")
                        .replace("\r", " ")
                        .replace("\t", " ");

        // Normalize whitespace
        content = content.replaceAll("\\s+", " ").trim();

        return content;
    }

    /**
     * Normalizes table data to ensure all rows have the same number of columns
     *
     * @param tableData the table data to normalize
     * @param maxCols the maximum number of columns
     */
    private void normalizeTableData(List<List<String>> tableData, int maxCols) {
        for (List<String> row : tableData) {
            while (row.size() < maxCols) {
                row.add("");
            }
            // Trim rows that are too long
            while (row.size() > maxCols) {
                row.remove(row.size() - 1);
            }
        }
    }

    /**
     * Writes an enhanced table with proper markdown formatting
     *
     * @param tableData the table data
     * @param maxCols the maximum number of columns
     * @param markdown the markdown builder
     * @param structure the sheet structure information
     */
    private void writeEnhancedTable(List<List<String>> tableData, int maxCols,
                                   StringBuilder markdown, SheetStructure structure) {
        if (tableData.isEmpty() || maxCols == 0) {
            return;
        }

        int headerRowCount = structure.hasHeader() ? 1 : 0;

        // Write header row(s)
        for (int i = 0; i < Math.min(headerRowCount, tableData.size()); i++) {
            writeTableRow(tableData.get(i), markdown);
            if (i == 0) {
                // Write separator after first header row
                writeTableSeparator(maxCols, markdown);
            }
        }

        // If no header was written, write the first row as header
        if (headerRowCount == 0 && !tableData.isEmpty()) {
            writeTableRow(tableData.get(0), markdown);
            writeTableSeparator(maxCols, markdown);
            headerRowCount = 1;
        }

        // Write data rows
        for (int i = headerRowCount; i < tableData.size(); i++) {
            writeTableRow(tableData.get(i), markdown);
        }
    }

    /**
     * Writes a table row in markdown format
     *
     * @param row the row data
     * @param markdown the markdown builder
     */
    private void writeTableRow(List<String> row, StringBuilder markdown) {
        markdown.append("|");
        for (String cell : row) {
            markdown.append(" ").append(cell).append(" |");
        }
        markdown.append("\n");
    }

    /**
     * Writes the table separator row
     *
     * @param numCols the number of columns
     * @param markdown the markdown builder
     */
    private void writeTableSeparator(int numCols, StringBuilder markdown) {
        markdown.append("|");
        for (int i = 0; i < numCols; i++) {
            markdown.append("---|");
        }
        markdown.append("\n");
    }

    /**
     * Caches a value with automatic cleanup management
     *
     * @param key the cache key
     * @param value the value to cache
     */
    private void cacheValueWithCleanup(String key, String value) {
        // Perform periodic cleanup every 5 minutes
        if (Duration.between(lastCleanup, LocalDateTime.now()).toMinutes() >= 5) {
            cleanupExpiredEntries();
            lastCleanup = LocalDateTime.now();
        }
        
        // Cache the value if under size limit
        if (CELL_CONTENT_CACHE.size() < MAX_CACHE_SIZE) {
            CELL_CONTENT_CACHE.put(key, new CacheEntry(value));
        } else {
            // If at limit, try to evict expired entries first
            cleanupExpiredEntries();
            if (CELL_CONTENT_CACHE.size() < MAX_CACHE_SIZE) {
                CELL_CONTENT_CACHE.put(key, new CacheEntry(value));
            }
            // If still at limit, evict oldest entry
            else {
                evictOldestEntry();
                CELL_CONTENT_CACHE.put(key, new CacheEntry(value));
            }
        }
    }
    
    /**
     * Cleans up expired cache entries
     */
    private static void cleanupExpiredEntries() {
        CELL_CONTENT_CACHE.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }
    
    /**
     * Evicts the oldest cache entry
     */
    private static void evictOldestEntry() {
        if (CELL_CONTENT_CACHE.isEmpty()) {
            return;
        }
        
        String oldestKey = null;
        LocalDateTime oldestTime = LocalDateTime.now();
        
        for (Map.Entry<String, CacheEntry> entry : CELL_CONTENT_CACHE.entrySet()) {
            if (entry.getValue().timestamp.isBefore(oldestTime)) {
                oldestTime = entry.getValue().timestamp;
                oldestKey = entry.getKey();
            }
        }
        
        if (oldestKey != null) {
            CELL_CONTENT_CACHE.remove(oldestKey);
        }
    }

    /**
     * Clears the content cache to free memory
     */
    public static void clearCache() {
        CELL_CONTENT_CACHE.clear();
        lastCleanup = LocalDateTime.now();
    }

    /**
     * Gets cache statistics for monitoring
     *
     * @return cache statistics
     */
    public static Map<String, Object> getCacheStatistics() {
        // Clean up expired entries for accurate stats
        cleanupExpiredEntries();
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("cellContentCacheSize", CELL_CONTENT_CACHE.size());
        stats.put("cellContentCacheLimit", MAX_CACHE_SIZE);
        stats.put("cacheUtilization", String.format("%.2f%%", 
            (CELL_CONTENT_CACHE.size() * 100.0) / MAX_CACHE_SIZE));
        stats.put("cacheTtlMinutes", CACHE_TTL.toMinutes());
        stats.put("lastCleanup", lastCleanup.toString());
        
        // Calculate cache age distribution
        long expiredEntries = CELL_CONTENT_CACHE.values().stream()
            .mapToLong(entry -> entry.isExpired() ? 1 : 0)
            .sum();
        stats.put("expiredEntries", expiredEntries);
        
        return stats;
    }
}

