package com.talkweb.ai.converter.vlm.controller;

import com.talkweb.ai.converter.vlm.cache.VlmCacheService;
import com.talkweb.ai.converter.vlm.config.VlmConfigurationManager;
import com.talkweb.ai.converter.vlm.monitor.VlmPerformanceMonitor;
import com.talkweb.ai.converter.vlm.service.VlmBatchProcessingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * VLM系统管理API控制器
 * 
 * 提供VLM系统的监控、配置和管理功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/vlm/admin")
@Tag(name = "VLM系统管理", description = "VLM系统监控和管理API")
public class VlmManagementController {
    
    private static final Logger log = LoggerFactory.getLogger(VlmManagementController.class);
    
    @Autowired
    private VlmConfigurationManager configManager;
    
    @Autowired
    private VlmPerformanceMonitor performanceMonitor;
    
    @Autowired
    private VlmCacheService cacheService;
    
    @Autowired
    private VlmBatchProcessingService batchProcessingService;
    
    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "系统健康检查", description = "获取VLM系统的整体健康状态")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 系统基本信息
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            health.put("status", "HEALTHY");
            
            // 配置状态
            VlmConfigurationManager.VlmConfigurationReport configReport = configManager.generateConfigurationReport();
            Map<String, Object> configStatus = new HashMap<>();
            configStatus.put("totalProviders", configReport.getTotalProviders());
            configStatus.put("availableProviders", configReport.getAvailableProviders());
            configStatus.put("recommendedModel", configReport.getRecommendedModel());
            configStatus.put("supportedModels", configReport.getTotalModels());
            health.put("configuration", configStatus);
            
            // 性能状态
            String performanceSummary = performanceMonitor.generateSummary();
            health.put("performanceSummary", performanceSummary);
            
            // 缓存状态
            VlmCacheService.CacheStatistics cacheStats = cacheService.getCacheStatistics();
            Map<String, Object> cacheStatus = new HashMap<>();
            cacheStatus.put("base64CacheSize", cacheStats.getBase64CacheSize());
            cacheStatus.put("vlmResultCacheSize", cacheStats.getVlmResultCacheSize());
            cacheStatus.put("base64HitRate", String.format("%.1f%%", cacheStats.getBase64HitRate()));
            cacheStatus.put("vlmHitRate", String.format("%.1f%%", cacheStats.getVlmHitRate()));
            cacheStatus.put("totalMemoryUsage", formatBytes(cacheStats.getTotalMemoryUsage()));
            health.put("cache", cacheStatus);
            
            // 批量处理状态
            List<VlmBatchProcessingService.BatchTask> activeTasks = batchProcessingService.getActiveTasks();
            Map<String, Object> batchStatus = new HashMap<>();
            batchStatus.put("activeTasks", activeTasks.size());
            batchStatus.put("tasks", activeTasks);
            health.put("batchProcessing", batchStatus);
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("系统健康检查失败: {}", e.getMessage(), e);
            
            Map<String, Object> error = new HashMap<>();
            error.put("status", "UNHEALTHY");
            error.put("error", e.getMessage());
            error.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
    
    /**
     * 获取详细配置信息
     */
    @GetMapping("/config/detailed")
    @Operation(summary = "获取详细配置", description = "获取VLM系统的详细配置信息")
    public ResponseEntity<VlmConfigurationManager.VlmConfigurationReport> getDetailedConfiguration() {
        try {
            VlmConfigurationManager.VlmConfigurationReport report = configManager.generateConfigurationReport();
            return ResponseEntity.ok(report);
        } catch (Exception e) {
            log.error("获取详细配置失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取配置指导
     */
    @GetMapping("/config/guide")
    @Operation(summary = "获取配置指导", description = "获取VLM配置的详细指导信息")
    public ResponseEntity<Map<String, Object>> getConfigurationGuide() {
        try {
            String guide = configManager.generateConfigurationGuide();
            
            Map<String, Object> response = new HashMap<>();
            response.put("guide", guide);
            response.put("availableProviders", configManager.getAvailableProviders());
            response.put("domesticProviders", configManager.getDomesticProviders());
            response.put("internationalProviders", configManager.getInternationalProviders());
            response.put("recommendedModel", configManager.getRecommendedModel());
            response.put("allSupportedModels", configManager.getAllSupportedModels());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取配置指导失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取详细性能报告
     */
    @GetMapping("/performance/detailed")
    @Operation(summary = "获取详细性能报告", description = "获取VLM系统的详细性能监控报告")
    public ResponseEntity<VlmPerformanceMonitor.VlmPerformanceReport> getDetailedPerformanceReport() {
        try {
            VlmPerformanceMonitor.VlmPerformanceReport report = performanceMonitor.generateReport();
            return ResponseEntity.ok(report);
        } catch (Exception e) {
            log.error("获取详细性能报告失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 重置性能统计
     */
    @PostMapping("/performance/reset")
    @Operation(summary = "重置性能统计", description = "重置VLM性能监控的统计数据")
    public ResponseEntity<Map<String, Object>> resetPerformanceStatistics() {
        try {
            performanceMonitor.resetStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "性能统计已重置");
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("重置性能统计失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "message", "重置失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取详细缓存统计
     */
    @GetMapping("/cache/detailed")
    @Operation(summary = "获取详细缓存统计", description = "获取VLM缓存系统的详细统计信息")
    public ResponseEntity<Map<String, Object>> getDetailedCacheStatistics() {
        try {
            VlmCacheService.CacheStatistics stats = cacheService.getCacheStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("base64Cache", Map.of(
                    "size", stats.getBase64CacheSize(),
                    "hitRate", String.format("%.2f%%", stats.getBase64HitRate()),
                    "memoryUsage", formatBytes(stats.getBase64CacheMemoryUsage())
            ));
            
            response.put("vlmResultCache", Map.of(
                    "size", stats.getVlmResultCacheSize(),
                    "hitRate", String.format("%.2f%%", stats.getVlmHitRate()),
                    "memoryUsage", formatBytes(stats.getVlmCacheMemoryUsage())
            ));
            
            response.put("imageCache", Map.of(
                    "size", stats.getImageCacheSize()
            ));
            
            response.put("total", Map.of(
                    "memoryUsage", formatBytes(stats.getTotalMemoryUsage()),
                    "cacheCount", stats.getBase64CacheSize() + stats.getVlmResultCacheSize() + stats.getImageCacheSize()
            ));
            
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取详细缓存统计失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 手动清理过期缓存
     */
    @PostMapping("/cache/cleanup")
    @Operation(summary = "清理过期缓存", description = "手动清理VLM缓存系统中的过期条目")
    public ResponseEntity<Map<String, Object>> cleanupExpiredCache() {
        try {
            VlmCacheService.CacheStatistics beforeStats = cacheService.getCacheStatistics();
            
            cacheService.cleanupExpiredCache();
            
            VlmCacheService.CacheStatistics afterStats = cacheService.getCacheStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "过期缓存清理完成");
            response.put("before", Map.of(
                    "base64Cache", beforeStats.getBase64CacheSize(),
                    "vlmResultCache", beforeStats.getVlmResultCacheSize(),
                    "imageCache", beforeStats.getImageCacheSize()
            ));
            response.put("after", Map.of(
                    "base64Cache", afterStats.getBase64CacheSize(),
                    "vlmResultCache", afterStats.getVlmResultCacheSize(),
                    "imageCache", afterStats.getImageCacheSize()
            ));
            response.put("freed", Map.of(
                    "base64Cache", beforeStats.getBase64CacheSize() - afterStats.getBase64CacheSize(),
                    "vlmResultCache", beforeStats.getVlmResultCacheSize() - afterStats.getVlmResultCacheSize(),
                    "imageCache", beforeStats.getImageCacheSize() - afterStats.getImageCacheSize()
            ));
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("清理过期缓存失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "message", "清理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 清理所有缓存
     */
    @DeleteMapping("/cache/all")
    @Operation(summary = "清理所有缓存", description = "清理VLM缓存系统中的所有缓存数据")
    public ResponseEntity<Map<String, Object>> clearAllCache() {
        try {
            VlmCacheService.CacheStatistics beforeStats = cacheService.getCacheStatistics();
            
            cacheService.clearAllCache();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "所有缓存已清理");
            response.put("clearedItems", Map.of(
                    "base64Cache", beforeStats.getBase64CacheSize(),
                    "vlmResultCache", beforeStats.getVlmResultCacheSize(),
                    "imageCache", beforeStats.getImageCacheSize()
            ));
            response.put("freedMemory", formatBytes(beforeStats.getTotalMemoryUsage()));
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("清理所有缓存失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "message", "清理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取批量处理状态
     */
    @GetMapping("/batch/status")
    @Operation(summary = "获取批量处理状态", description = "获取所有活跃批量处理任务的状态")
    public ResponseEntity<Map<String, Object>> getBatchProcessingStatus() {
        try {
            List<VlmBatchProcessingService.BatchTask> activeTasks = batchProcessingService.getActiveTasks();
            
            Map<String, Object> response = new HashMap<>();
            response.put("activeTasks", activeTasks.size());
            response.put("tasks", activeTasks);
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取批量处理状态失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 清理已完成的批量任务
     */
    @PostMapping("/batch/cleanup")
    @Operation(summary = "清理已完成任务", description = "清理已完成的批量处理任务")
    public ResponseEntity<Map<String, Object>> cleanupCompletedBatchTasks() {
        try {
            int beforeCount = batchProcessingService.getActiveTasks().size();
            
            batchProcessingService.cleanupCompletedTasks();
            
            int afterCount = batchProcessingService.getActiveTasks().size();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "已完成任务清理完成");
            response.put("tasksBefore", beforeCount);
            response.put("tasksAfter", afterCount);
            response.put("cleanedTasks", beforeCount - afterCount);
            response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("清理已完成任务失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "message", "清理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取特定批量任务详情
     */
    @GetMapping("/batch/{batchId}")
    @Operation(summary = "获取批量任务详情", description = "获取指定批量任务的详细信息")
    public ResponseEntity<VlmBatchProcessingService.BatchTask> getBatchTaskDetails(
            @Parameter(description = "批次ID", required = true)
            @PathVariable String batchId) {
        
        try {
            VlmBatchProcessingService.BatchTask task = batchProcessingService.getBatchTaskStatus(batchId);
            if (task != null) {
                return ResponseEntity.ok(task);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取批量任务详情失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取系统信息摘要
     */
    @GetMapping("/summary")
    @Operation(summary = "获取系统摘要", description = "获取VLM系统的简要摘要信息")
    public ResponseEntity<Map<String, Object>> getSystemSummary() {
        try {
            Map<String, Object> summary = new HashMap<>();
            
            // 配置摘要
            VlmConfigurationManager.VlmConfigurationReport configReport = configManager.generateConfigurationReport();
            summary.put("config", Map.of(
                    "availableProviders", configReport.getAvailableProviders() + "/" + configReport.getTotalProviders(),
                    "recommendedModel", configReport.getRecommendedModel(),
                    "totalModels", configReport.getTotalModels()
            ));
            
            // 性能摘要
            summary.put("performance", performanceMonitor.generateSummary());
            
            // 缓存摘要
            VlmCacheService.CacheStatistics cacheStats = cacheService.getCacheStatistics();
            summary.put("cache", Map.of(
                    "totalSize", cacheStats.getBase64CacheSize() + cacheStats.getVlmResultCacheSize(),
                    "hitRate", String.format("%.1f%%", (cacheStats.getBase64HitRate() + cacheStats.getVlmHitRate()) / 2),
                    "memoryUsage", formatBytes(cacheStats.getTotalMemoryUsage())
            ));
            
            // 批量处理摘要
            List<VlmBatchProcessingService.BatchTask> activeTasks = batchProcessingService.getActiveTasks();
            summary.put("batch", Map.of(
                    "activeTasks", activeTasks.size()
            ));
            
            summary.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            summary.put("status", "OPERATIONAL");
            
            return ResponseEntity.ok(summary);
            
        } catch (Exception e) {
            log.error("获取系统摘要失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("status", "ERROR", "message", e.getMessage()));
        }
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    /**
     * 全局异常处理
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        log.error("VLM管理控制器未处理异常: {}", e.getMessage(), e);
        
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("message", "服务器内部错误: " + e.getMessage());
        error.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}