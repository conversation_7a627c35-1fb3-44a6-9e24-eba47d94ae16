package com.talkweb.ai.converter.vlm.service;

import com.talkweb.ai.converter.vlm.config.VlmConfigurationManager;
import com.talkweb.ai.converter.vlm.model.VlmConversionRequest;
import com.talkweb.ai.converter.vlm.model.VlmConversionResult;
import com.talkweb.ai.converter.vlm.monitor.VlmPerformanceMonitor;
import com.talkweb.ai.converter.vlm.service.impl.VisionLanguageModelService;
import com.talkweb.ai.converter.vlm.util.VlmImageProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * VLM批量处理服务
 * 
 * 提供高效的批量文档转换和处理能力
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Service
public class VlmBatchProcessingService {
    
    private static final Logger log = LoggerFactory.getLogger(VlmBatchProcessingService.class);
    
    @Autowired
    private VisionLanguageModelService vlmService;
    
    @Autowired
    private VlmConfigurationManager configManager;
    
    @Autowired
    private VlmPerformanceMonitor performanceMonitor;
    
    // 批量处理线程池
    private final ExecutorService batchExecutor = Executors.newFixedThreadPool(8);
    
    // 活跃任务跟踪
    private final Map<String, BatchTask> activeTasks = new ConcurrentHashMap<>();
    
    /**
     * 批量处理图像文件
     */
    public CompletableFuture<BatchProcessingResult> batchProcessImages(
            List<File> imageFiles, VlmConversionRequest request) {
        
        String batchId = generateBatchId();
        log.info("开始批量处理 {} 个图像文件，批次ID: {}", imageFiles.size(), batchId);
        
        BatchTask task = new BatchTask(batchId, imageFiles.size());
        activeTasks.put(batchId, task);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return processBatch(batchId, imageFiles, request, task);
            } catch (Exception e) {
                log.error("批量处理失败，批次ID: {}", batchId, e);
                task.setStatus(BatchTaskStatus.FAILED);
                task.setErrorMessage(e.getMessage());
                throw new RuntimeException("批量处理失败: " + e.getMessage(), e);
            }
        }, batchExecutor);
    }
    
    /**
     * 执行批量处理
     */
    private BatchProcessingResult processBatch(String batchId, List<File> imageFiles, 
                                             VlmConversionRequest request, BatchTask task) {
        
        long startTime = System.currentTimeMillis();
        task.setStatus(BatchTaskStatus.PROCESSING);
        
        // 选择最优模型
        String optimalModel = selectOptimalModel(request);
        request.setVlmModel(optimalModel);
        
        List<ImageProcessingResult> results = new ArrayList<>();
        List<CompletableFuture<ImageProcessingResult>> futures = new ArrayList<>();
        
        // 创建处理任务
        for (int i = 0; i < imageFiles.size(); i++) {
            final int index = i;
            final File imageFile = imageFiles.get(i);
            
            CompletableFuture<ImageProcessingResult> future = CompletableFuture.supplyAsync(() -> {
                return processImageWithTracking(batchId, index, imageFile, request, task);
            }, batchExecutor);
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            
            // 收集结果
            for (CompletableFuture<ImageProcessingResult> future : futures) {
                try {
                    ImageProcessingResult result = future.get();
                    if (result != null) {
                        results.add(result);
                    }
                } catch (Exception e) {
                    log.error("获取处理结果失败: {}", e.getMessage());
                }
            }
            
        } catch (InterruptedException | ExecutionException e) {
            log.error("批量处理中断，批次ID: {}", batchId, e);
            task.setStatus(BatchTaskStatus.FAILED);
            task.setErrorMessage("处理中断: " + e.getMessage());
        }
        
        // 完成处理
        long totalTime = System.currentTimeMillis() - startTime;
        task.setCompletionTime(System.currentTimeMillis());
        task.setStatus(BatchTaskStatus.COMPLETED);
        
        // 生成批量处理结果
        BatchProcessingResult batchResult = createBatchResult(batchId, results, totalTime, optimalModel);
        
        log.info("批量处理完成，批次ID: {}, 成功: {}/{}, 耗时: {}ms", 
                batchId, batchResult.getSuccessfulCount(), 
                batchResult.getTotalCount(), totalTime);
        
        return batchResult;
    }
    
    /**
     * 处理单个图像并跟踪进度
     */
    private ImageProcessingResult processImageWithTracking(String batchId, int index, 
                                                          File imageFile, VlmConversionRequest request, 
                                                          BatchTask task) {
        
        VlmPerformanceMonitor.VlmRequestTracker tracker = null;
        ImageProcessingResult result = new ImageProcessingResult();
        result.setImageIndex(index);
        result.setImageFile(imageFile);
        result.setStartTime(System.currentTimeMillis());
        
        try {
            // 开始性能监控
            tracker = performanceMonitor.startRequest(request.getVlmModel(), batchId + "-" + index);
            
            // 处理图像
            VlmImageProcessor.ProcessedImage processedImage = VlmImageProcessor.processImage(imageFile);
            
            // VLM识别
            List<File> singleImageList = Arrays.asList(imageFile);
            VlmConversionResult.VlmRecognitionResult vlmResult = 
                    vlmService.recognizeImages(singleImageList, request).get();
            
            if (vlmResult.isSuccess()) {
                result.setSuccess(true);
                result.setMarkdownContent(vlmResult.getCombinedMarkdown());
                result.setImageProcessingStats(VlmImageProcessor.createStats(processedImage));
                
                performanceMonitor.recordSuccess(tracker, vlmResult.getCombinedMarkdown().length());
                log.debug("图像处理成功: {} [{}]", imageFile.getName(), batchId);
            } else {
                result.setSuccess(false);
                result.setErrorMessage(vlmResult.getErrorMessage());
                
                performanceMonitor.recordFailure(tracker, "vlm_recognition_failed", vlmResult.getErrorMessage());
                log.warn("图像处理失败: {} [{}] - {}", imageFile.getName(), batchId, vlmResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("处理异常: " + e.getMessage());
            
            if (tracker != null) {
                performanceMonitor.recordFailure(tracker, "processing_exception", e.getMessage());
            }
            
            log.error("图像处理异常: {} [{}]", imageFile.getName(), batchId, e);
        }
        
        result.setEndTime(System.currentTimeMillis());
        result.setProcessingTime(result.getEndTime() - result.getStartTime());
        
        // 更新任务进度
        task.incrementCompletedCount();
        updateTaskProgress(task);
        
        return result;
    }
    
    /**
     * 选择最优模型
     */
    private String selectOptimalModel(VlmConversionRequest request) {
        String requestedModel = request.getVlmModel();
        
        // 如果请求了特定模型且可用，使用该模型
        if (requestedModel != null && !requestedModel.isEmpty()) {
            VlmConfigurationManager.VlmProvider provider = configManager.getProviderForModel(requestedModel);
            if (provider != null && provider.isAvailable()) {
                return requestedModel;
            }
        }
        
        // 否则使用推荐模型
        return configManager.getRecommendedModel();
    }
    
    /**
     * 创建批量处理结果
     */
    private BatchProcessingResult createBatchResult(String batchId, List<ImageProcessingResult> results, 
                                                   long totalTime, String modelUsed) {
        
        BatchProcessingResult batchResult = new BatchProcessingResult();
        batchResult.setBatchId(batchId);
        batchResult.setTotalCount(results.size());
        batchResult.setProcessingResults(results);
        batchResult.setTotalProcessingTime(totalTime);
        batchResult.setModelUsed(modelUsed);
        
        // 统计成功失败数量
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        batchResult.setSuccessfulCount((int) successCount);
        batchResult.setFailedCount(results.size() - (int) successCount);
        
        // 计算平均处理时间
        double avgTime = results.stream().mapToLong(ImageProcessingResult::getProcessingTime).average().orElse(0.0);
        batchResult.setAverageProcessingTime(avgTime);
        
        // 计算成功率
        batchResult.setSuccessRate(results.isEmpty() ? 0.0 : (double) successCount / results.size() * 100);
        
        // 生成合并的Markdown内容
        StringBuilder combinedMarkdown = new StringBuilder();
        combinedMarkdown.append("# 批量处理结果\n\n");
        combinedMarkdown.append(String.format("批次ID: %s\n", batchId));
        combinedMarkdown.append(String.format("处理时间: %s\n", new Date()));
        combinedMarkdown.append(String.format("使用模型: %s\n", modelUsed));
        combinedMarkdown.append(String.format("成功率: %.1f%% (%d/%d)\n\n", 
                batchResult.getSuccessRate(), batchResult.getSuccessfulCount(), batchResult.getTotalCount()));
        
        combinedMarkdown.append("---\n\n");
        
        for (ImageProcessingResult result : results) {
            combinedMarkdown.append(String.format("## 文件 %d: %s\n\n", 
                    result.getImageIndex() + 1, result.getImageFile().getName()));
            
            if (result.isSuccess()) {
                combinedMarkdown.append(result.getMarkdownContent());
            } else {
                combinedMarkdown.append(String.format("**处理失败**: %s\n", result.getErrorMessage()));
            }
            
            combinedMarkdown.append("\n\n---\n\n");
        }
        
        batchResult.setCombinedMarkdown(combinedMarkdown.toString());
        
        return batchResult;
    }
    
    /**
     * 更新任务进度
     */
    private void updateTaskProgress(BatchTask task) {
        double progress = (double) task.getCompletedCount() / task.getTotalCount() * 100;
        task.setProgress(progress);
        
        if (task.getCompletedCount() % 10 == 0 || task.getCompletedCount() == task.getTotalCount()) {
            log.info("批量处理进度 [{}]: {:.1f}% ({}/{})", 
                    task.getBatchId(), progress, task.getCompletedCount(), task.getTotalCount());
        }
    }
    
    /**
     * 获取批量任务状态
     */
    public BatchTask getBatchTaskStatus(String batchId) {
        return activeTasks.get(batchId);
    }
    
    /**
     * 获取所有活跃任务
     */
    public List<BatchTask> getActiveTasks() {
        return new ArrayList<>(activeTasks.values());
    }
    
    /**
     * 清理已完成的任务
     */
    public void cleanupCompletedTasks() {
        long cutoffTime = System.currentTimeMillis() - 3600000; // 1小时前
        activeTasks.entrySet().removeIf(entry -> {
            BatchTask task = entry.getValue();
            return (task.getStatus() == BatchTaskStatus.COMPLETED || task.getStatus() == BatchTaskStatus.FAILED) 
                    && task.getCompletionTime() < cutoffTime;
        });
        
        log.info("清理已完成任务，当前活跃任务数: {}", activeTasks.size());
    }
    
    /**
     * 生成批次ID
     */
    private String generateBatchId() {
        return "batch_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString(new Random().nextInt(0x10000));
    }
    
    /**
     * 批量任务状态枚举
     */
    public enum BatchTaskStatus {
        PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED
    }
    
    /**
     * 批量任务信息
     */
    public static class BatchTask {
        private String batchId;
        private int totalCount;
        private int completedCount;
        private double progress;
        private BatchTaskStatus status;
        private long startTime;
        private long completionTime;
        private String errorMessage;
        
        public BatchTask(String batchId, int totalCount) {
            this.batchId = batchId;
            this.totalCount = totalCount;
            this.completedCount = 0;
            this.progress = 0.0;
            this.status = BatchTaskStatus.PENDING;
            this.startTime = System.currentTimeMillis();
        }
        
        public void incrementCompletedCount() {
            this.completedCount++;
        }
        
        // Getters and Setters
        public String getBatchId() { return batchId; }
        public int getTotalCount() { return totalCount; }
        public int getCompletedCount() { return completedCount; }
        public double getProgress() { return progress; }
        public void setProgress(double progress) { this.progress = progress; }
        public BatchTaskStatus getStatus() { return status; }
        public void setStatus(BatchTaskStatus status) { this.status = status; }
        public long getStartTime() { return startTime; }
        public long getCompletionTime() { return completionTime; }
        public void setCompletionTime(long completionTime) { this.completionTime = completionTime; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    /**
     * 图像处理结果
     */
    public static class ImageProcessingResult {
        private int imageIndex;
        private File imageFile;
        private boolean success;
        private String markdownContent;
        private String errorMessage;
        private long startTime;
        private long endTime;
        private long processingTime;
        private VlmImageProcessor.ImageProcessingStats imageProcessingStats;
        
        // Getters and Setters
        public int getImageIndex() { return imageIndex; }
        public void setImageIndex(int imageIndex) { this.imageIndex = imageIndex; }
        
        public File getImageFile() { return imageFile; }
        public void setImageFile(File imageFile) { this.imageFile = imageFile; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMarkdownContent() { return markdownContent; }
        public void setMarkdownContent(String markdownContent) { this.markdownContent = markdownContent; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getProcessingTime() { return processingTime; }
        public void setProcessingTime(long processingTime) { this.processingTime = processingTime; }
        
        public VlmImageProcessor.ImageProcessingStats getImageProcessingStats() { return imageProcessingStats; }
        public void setImageProcessingStats(VlmImageProcessor.ImageProcessingStats imageProcessingStats) { 
            this.imageProcessingStats = imageProcessingStats; 
        }
    }
    
    /**
     * 批量处理结果
     */
    public static class BatchProcessingResult {
        private String batchId;
        private int totalCount;
        private int successfulCount;
        private int failedCount;
        private double successRate;
        private long totalProcessingTime;
        private double averageProcessingTime;
        private String modelUsed;
        private List<ImageProcessingResult> processingResults;
        private String combinedMarkdown;
        
        // Getters and Setters
        public String getBatchId() { return batchId; }
        public void setBatchId(String batchId) { this.batchId = batchId; }
        
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessfulCount() { return successfulCount; }
        public void setSuccessfulCount(int successfulCount) { this.successfulCount = successfulCount; }
        
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
        
        public long getTotalProcessingTime() { return totalProcessingTime; }
        public void setTotalProcessingTime(long totalProcessingTime) { this.totalProcessingTime = totalProcessingTime; }
        
        public double getAverageProcessingTime() { return averageProcessingTime; }
        public void setAverageProcessingTime(double averageProcessingTime) { this.averageProcessingTime = averageProcessingTime; }
        
        public String getModelUsed() { return modelUsed; }
        public void setModelUsed(String modelUsed) { this.modelUsed = modelUsed; }
        
        public List<ImageProcessingResult> getProcessingResults() { return processingResults; }
        public void setProcessingResults(List<ImageProcessingResult> processingResults) { this.processingResults = processingResults; }
        
        public String getCombinedMarkdown() { return combinedMarkdown; }
        public void setCombinedMarkdown(String combinedMarkdown) { this.combinedMarkdown = combinedMarkdown; }
    }
}