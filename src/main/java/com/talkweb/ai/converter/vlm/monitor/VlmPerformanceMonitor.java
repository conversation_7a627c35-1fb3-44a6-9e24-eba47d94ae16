package com.talkweb.ai.converter.vlm.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * VLM性能监控器
 * 
 * 监控VLM服务的性能指标、成功率、响应时间等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Component
public class VlmPerformanceMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(VlmPerformanceMonitor.class);
    
    // 基础统计
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong fallbackRequests = new AtomicLong(0);
    
    // 响应时间统计
    private final LongAdder totalResponseTime = new LongAdder();
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    
    // 模型使用统计
    private final Map<String, ModelStats> modelStatsMap = new ConcurrentHashMap<>();
    
    // 错误统计
    private final Map<String, AtomicLong> errorTypeCount = new ConcurrentHashMap<>();
    
    // 时间段统计（每小时）
    private final Map<String, HourlyStats> hourlyStatsMap = new ConcurrentHashMap<>();
    
    // 服务启动时间
    private final LocalDateTime startTime = LocalDateTime.now();
    
    @PostConstruct
    public void initialize() {
        log.info("VLM性能监控器已启动，开始时间: {}", startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
    }
    
    /**
     * 记录请求开始
     */
    public VlmRequestTracker startRequest(String model, String requestId) {
        totalRequests.incrementAndGet();
        updateHourlyStats(stats -> stats.requests.increment());
        
        getOrCreateModelStats(model).requestCount.incrementAndGet();
        
        return new VlmRequestTracker(model, requestId, System.currentTimeMillis());
    }
    
    /**
     * 记录请求成功
     */
    public void recordSuccess(VlmRequestTracker tracker, int contentLength) {
        long responseTime = System.currentTimeMillis() - tracker.startTime;
        
        successfulRequests.incrementAndGet();
        updateResponseTime(responseTime);
        updateHourlyStats(stats -> {
            stats.successfulRequests.increment();
            stats.totalResponseTime.add(responseTime);
        });
        
        ModelStats modelStats = getOrCreateModelStats(tracker.model);
        modelStats.successCount.incrementAndGet();
        modelStats.totalResponseTime.addAndGet(responseTime);
        modelStats.totalContentLength.addAndGet(contentLength);
        
        log.debug("VLM请求成功: 模型={}, 响应时间={}ms, 内容长度={}", 
                 tracker.model, responseTime, contentLength);
    }
    
    /**
     * 记录请求失败
     */
    public void recordFailure(VlmRequestTracker tracker, String errorType, String errorMessage) {
        long responseTime = System.currentTimeMillis() - tracker.startTime;
        
        failedRequests.incrementAndGet();
        updateResponseTime(responseTime);
        updateHourlyStats(stats -> stats.failedRequests.increment());
        
        ModelStats modelStats = getOrCreateModelStats(tracker.model);
        modelStats.failureCount.incrementAndGet();
        
        // 记录错误类型
        errorTypeCount.computeIfAbsent(errorType, k -> new AtomicLong(0)).incrementAndGet();
        
        log.warn("VLM请求失败: 模型={}, 错误类型={}, 响应时间={}ms, 错误信息={}", 
                tracker.model, errorType, responseTime, errorMessage);
    }
    
    /**
     * 记录回退模式使用
     */
    public void recordFallback(VlmRequestTracker tracker, String reason) {
        long responseTime = System.currentTimeMillis() - tracker.startTime;
        
        fallbackRequests.incrementAndGet();
        updateHourlyStats(stats -> stats.fallbackRequests.increment());
        
        ModelStats modelStats = getOrCreateModelStats(tracker.model);
        modelStats.fallbackCount.incrementAndGet();
        
        log.info("VLM使用回退模式: 模型={}, 原因={}, 响应时间={}ms", 
                tracker.model, reason, responseTime);
    }
    
    /**
     * 更新响应时间统计
     */
    private void updateResponseTime(long responseTime) {
        totalResponseTime.add(responseTime);
        
        // 更新最小响应时间
        minResponseTime.updateAndGet(current -> Math.min(current, responseTime));
        
        // 更新最大响应时间
        maxResponseTime.updateAndGet(current -> Math.max(current, responseTime));
    }
    
    /**
     * 更新小时统计
     */
    private void updateHourlyStats(java.util.function.Consumer<HourlyStats> updater) {
        String hourKey = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH"));
        HourlyStats stats = hourlyStatsMap.computeIfAbsent(hourKey, k -> new HourlyStats());
        updater.accept(stats);
    }
    
    /**
     * 获取或创建模型统计
     */
    private ModelStats getOrCreateModelStats(String model) {
        return modelStatsMap.computeIfAbsent(model, k -> new ModelStats());
    }
    
    /**
     * 生成性能报告
     */
    public VlmPerformanceReport generateReport() {
        VlmPerformanceReport report = new VlmPerformanceReport();
        
        // 基础统计
        report.setStartTime(startTime);
        report.setReportTime(LocalDateTime.now());
        report.setTotalRequests(totalRequests.get());
        report.setSuccessfulRequests(successfulRequests.get());
        report.setFailedRequests(failedRequests.get());
        report.setFallbackRequests(fallbackRequests.get());
        
        // 成功率
        long total = totalRequests.get();
        if (total > 0) {
            report.setSuccessRate((double) successfulRequests.get() / total * 100);
            report.setFailureRate((double) failedRequests.get() / total * 100);
            report.setFallbackRate((double) fallbackRequests.get() / total * 100);
        }
        
        // 响应时间统计
        if (total > 0) {
            report.setAverageResponseTime((double) totalResponseTime.sum() / total);
            report.setMinResponseTime(minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get());
            report.setMaxResponseTime(maxResponseTime.get());
        }
        
        // 模型统计
        Map<String, ModelPerformance> modelPerformances = new HashMap<>();
        for (Map.Entry<String, ModelStats> entry : modelStatsMap.entrySet()) {
            String model = entry.getKey();
            ModelStats stats = entry.getValue();
            
            ModelPerformance performance = new ModelPerformance();
            performance.setModel(model);
            performance.setRequestCount(stats.requestCount.get());
            performance.setSuccessCount(stats.successCount.get());
            performance.setFailureCount(stats.failureCount.get());
            performance.setFallbackCount(stats.fallbackCount.get());
            
            long modelRequests = stats.requestCount.get();
            if (modelRequests > 0) {
                performance.setSuccessRate((double) stats.successCount.get() / modelRequests * 100);
                performance.setAverageResponseTime((double) stats.totalResponseTime.get() / modelRequests);
                performance.setAverageContentLength((double) stats.totalContentLength.get() / stats.successCount.get());
            }
            
            modelPerformances.put(model, performance);
        }
        report.setModelPerformances(modelPerformances);
        
        // 错误统计
        report.setErrorStatistics(new HashMap<>());
        errorTypeCount.forEach((errorType, count) -> 
                report.getErrorStatistics().put(errorType, count.get()));
        
        // 小时统计（最近24小时）
        Map<String, HourlyPerformance> hourlyPerformances = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < 24; i++) {
            LocalDateTime hour = now.minusHours(i);
            String hourKey = hour.format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH"));
            HourlyStats stats = hourlyStatsMap.get(hourKey);
            
            HourlyPerformance performance = new HourlyPerformance();
            performance.setHour(hour);
            if (stats != null) {
                performance.setRequests(stats.requests.longValue());
                performance.setSuccessfulRequests(stats.successfulRequests.longValue());
                performance.setFailedRequests(stats.failedRequests.longValue());
                performance.setFallbackRequests(stats.fallbackRequests.longValue());
                performance.setAverageResponseTime(
                        stats.requests.longValue() > 0 ? 
                        (double) stats.totalResponseTime.sum() / stats.requests.longValue() : 0);
            }
            
            hourlyPerformances.put(hourKey, performance);
        }
        report.setHourlyPerformances(hourlyPerformances);
        
        return report;
    }
    
    /**
     * 生成简要统计摘要
     */
    public String generateSummary() {
        long total = totalRequests.get();
        if (total == 0) {
            return "📊 VLM服务统计：暂无请求";
        }
        
        double successRate = (double) successfulRequests.get() / total * 100;
        double avgResponseTime = (double) totalResponseTime.sum() / total;
        
        StringBuilder summary = new StringBuilder();
        summary.append("📊 VLM服务统计摘要\n");
        summary.append(String.format("总请求数: %d | 成功率: %.1f%% | 平均响应时间: %.0fms\n", 
                total, successRate, avgResponseTime));
        summary.append(String.format("成功: %d | 失败: %d | 回退: %d\n", 
                successfulRequests.get(), failedRequests.get(), fallbackRequests.get()));
        
        // 最活跃的模型
        String mostUsedModel = modelStatsMap.entrySet().stream()
                .max(Map.Entry.comparingByValue((stats1, stats2) -> 
                        Long.compare(stats1.requestCount.get(), stats2.requestCount.get())))
                .map(Map.Entry::getKey)
                .orElse("无");
        
        summary.append(String.format("最常用模型: %s | 运行时长: %s", 
                mostUsedModel, formatDuration(startTime, LocalDateTime.now())));
        
        return summary.toString();
    }
    
    /**
     * 格式化持续时间
     */
    private String formatDuration(LocalDateTime start, LocalDateTime end) {
        long hours = java.time.Duration.between(start, end).toHours();
        if (hours < 24) {
            return hours + "小时";
        } else {
            return (hours / 24) + "天" + (hours % 24) + "小时";
        }
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        fallbackRequests.set(0);
        
        totalResponseTime.reset();
        minResponseTime.set(Long.MAX_VALUE);
        maxResponseTime.set(0);
        
        modelStatsMap.clear();
        errorTypeCount.clear();
        hourlyStatsMap.clear();
        
        log.info("VLM性能统计已重置");
    }
    
    /**
     * 请求跟踪器
     */
    public static class VlmRequestTracker {
        private final String model;
        private final String requestId;
        private final long startTime;
        
        public VlmRequestTracker(String model, String requestId, long startTime) {
            this.model = model;
            this.requestId = requestId;
            this.startTime = startTime;
        }
        
        public String getModel() { return model; }
        public String getRequestId() { return requestId; }
        public long getStartTime() { return startTime; }
    }
    
    /**
     * 模型统计信息
     */
    private static class ModelStats {
        private final AtomicLong requestCount = new AtomicLong(0);
        private final AtomicLong successCount = new AtomicLong(0);
        private final AtomicLong failureCount = new AtomicLong(0);
        private final AtomicLong fallbackCount = new AtomicLong(0);
        private final AtomicLong totalResponseTime = new AtomicLong(0);
        private final AtomicLong totalContentLength = new AtomicLong(0);
    }
    
    /**
     * 小时统计信息
     */
    private static class HourlyStats {
        private final LongAdder requests = new LongAdder();
        private final LongAdder successfulRequests = new LongAdder();
        private final LongAdder failedRequests = new LongAdder();
        private final LongAdder fallbackRequests = new LongAdder();
        private final LongAdder totalResponseTime = new LongAdder();
    }
    
    /**
     * 性能报告
     */
    public static class VlmPerformanceReport {
        private LocalDateTime startTime;
        private LocalDateTime reportTime;
        private long totalRequests;
        private long successfulRequests;
        private long failedRequests;
        private long fallbackRequests;
        private double successRate;
        private double failureRate;
        private double fallbackRate;
        private double averageResponseTime;
        private long minResponseTime;
        private long maxResponseTime;
        private Map<String, ModelPerformance> modelPerformances;
        private Map<String, Long> errorStatistics;
        private Map<String, HourlyPerformance> hourlyPerformances;
        
        // Getters and Setters
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getReportTime() { return reportTime; }
        public void setReportTime(LocalDateTime reportTime) { this.reportTime = reportTime; }
        
        public long getTotalRequests() { return totalRequests; }
        public void setTotalRequests(long totalRequests) { this.totalRequests = totalRequests; }
        
        public long getSuccessfulRequests() { return successfulRequests; }
        public void setSuccessfulRequests(long successfulRequests) { this.successfulRequests = successfulRequests; }
        
        public long getFailedRequests() { return failedRequests; }
        public void setFailedRequests(long failedRequests) { this.failedRequests = failedRequests; }
        
        public long getFallbackRequests() { return fallbackRequests; }
        public void setFallbackRequests(long fallbackRequests) { this.fallbackRequests = fallbackRequests; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
        
        public double getFailureRate() { return failureRate; }
        public void setFailureRate(double failureRate) { this.failureRate = failureRate; }
        
        public double getFallbackRate() { return fallbackRate; }
        public void setFallbackRate(double fallbackRate) { this.fallbackRate = fallbackRate; }
        
        public double getAverageResponseTime() { return averageResponseTime; }
        public void setAverageResponseTime(double averageResponseTime) { this.averageResponseTime = averageResponseTime; }
        
        public long getMinResponseTime() { return minResponseTime; }
        public void setMinResponseTime(long minResponseTime) { this.minResponseTime = minResponseTime; }
        
        public long getMaxResponseTime() { return maxResponseTime; }
        public void setMaxResponseTime(long maxResponseTime) { this.maxResponseTime = maxResponseTime; }
        
        public Map<String, ModelPerformance> getModelPerformances() { return modelPerformances; }
        public void setModelPerformances(Map<String, ModelPerformance> modelPerformances) { this.modelPerformances = modelPerformances; }
        
        public Map<String, Long> getErrorStatistics() { return errorStatistics; }
        public void setErrorStatistics(Map<String, Long> errorStatistics) { this.errorStatistics = errorStatistics; }
        
        public Map<String, HourlyPerformance> getHourlyPerformances() { return hourlyPerformances; }
        public void setHourlyPerformances(Map<String, HourlyPerformance> hourlyPerformances) { this.hourlyPerformances = hourlyPerformances; }
    }
    
    /**
     * 模型性能信息
     */
    public static class ModelPerformance {
        private String model;
        private long requestCount;
        private long successCount;
        private long failureCount;
        private long fallbackCount;
        private double successRate;
        private double averageResponseTime;
        private double averageContentLength;
        
        // Getters and Setters
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        
        public long getRequestCount() { return requestCount; }
        public void setRequestCount(long requestCount) { this.requestCount = requestCount; }
        
        public long getSuccessCount() { return successCount; }
        public void setSuccessCount(long successCount) { this.successCount = successCount; }
        
        public long getFailureCount() { return failureCount; }
        public void setFailureCount(long failureCount) { this.failureCount = failureCount; }
        
        public long getFallbackCount() { return fallbackCount; }
        public void setFallbackCount(long fallbackCount) { this.fallbackCount = fallbackCount; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
        
        public double getAverageResponseTime() { return averageResponseTime; }
        public void setAverageResponseTime(double averageResponseTime) { this.averageResponseTime = averageResponseTime; }
        
        public double getAverageContentLength() { return averageContentLength; }
        public void setAverageContentLength(double averageContentLength) { this.averageContentLength = averageContentLength; }
    }
    
    /**
     * 小时性能信息
     */
    public static class HourlyPerformance {
        private LocalDateTime hour;
        private long requests;
        private long successfulRequests;
        private long failedRequests;
        private long fallbackRequests;
        private double averageResponseTime;
        
        // Getters and Setters
        public LocalDateTime getHour() { return hour; }
        public void setHour(LocalDateTime hour) { this.hour = hour; }
        
        public long getRequests() { return requests; }
        public void setRequests(long requests) { this.requests = requests; }
        
        public long getSuccessfulRequests() { return successfulRequests; }
        public void setSuccessfulRequests(long successfulRequests) { this.successfulRequests = successfulRequests; }
        
        public long getFailedRequests() { return failedRequests; }
        public void setFailedRequests(long failedRequests) { this.failedRequests = failedRequests; }
        
        public long getFallbackRequests() { return fallbackRequests; }
        public void setFallbackRequests(long fallbackRequests) { this.fallbackRequests = fallbackRequests; }
        
        public double getAverageResponseTime() { return averageResponseTime; }
        public void setAverageResponseTime(double averageResponseTime) { this.averageResponseTime = averageResponseTime; }
    }
}