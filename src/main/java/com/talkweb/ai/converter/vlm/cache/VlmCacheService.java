package com.talkweb.ai.converter.vlm.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * VLM智能缓存服务
 * 
 * 提供图像处理结果的智能缓存，避免重复处理相同图像
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Service
public class VlmCacheService {
    
    private static final Logger log = LoggerFactory.getLogger(VlmCacheService.class);
    
    // 图像哈希缓存
    private final Map<String, CachedImageResult> imageCache = new ConcurrentHashMap<>();
    
    // Base64缓存
    private final Map<String, CachedBase64Result> base64Cache = new ConcurrentHashMap<>();
    
    // VLM结果缓存
    private final Map<String, CachedVlmResult> vlmResultCache = new ConcurrentHashMap<>();
    
    // 缓存配置
    private final long DEFAULT_TTL_HOURS = 24; // 24小时
    private final int MAX_CACHE_SIZE = 1000;
    private final long MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    
    // 定时清理任务
    private final ScheduledExecutorService cleanupScheduler = Executors.newSingleThreadScheduledExecutor();
    
    public VlmCacheService() {
        // 每小时清理过期缓存
        cleanupScheduler.scheduleAtFixedRate(this::cleanupExpiredCache, 1, 1, TimeUnit.HOURS);
        log.info("VLM缓存服务已启动，TTL: {}小时, 最大缓存: {}", DEFAULT_TTL_HOURS, MAX_CACHE_SIZE);
    }
    
    /**
     * 获取或计算图像文件的Base64编码
     */
    public String getOrComputeBase64(File imageFile) throws IOException {
        String fileHash = calculateFileHash(imageFile);
        String cacheKey = "base64_" + fileHash;
        
        CachedBase64Result cached = base64Cache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            log.debug("Base64缓存命中: {}", imageFile.getName());
            cached.incrementHitCount();
            return cached.getBase64Data();
        }
        
        // 计算Base64
        byte[] imageBytes = Files.readAllBytes(imageFile.toPath());
        String base64Data = Base64.getEncoder().encodeToString(imageBytes);
        
        // 缓存结果
        CachedBase64Result result = new CachedBase64Result();
        result.setFileHash(fileHash);
        result.setFileName(imageFile.getName());
        result.setFileSize(imageFile.length());
        result.setBase64Data(base64Data);
        result.setCreationTime(LocalDateTime.now());
        result.setExpirationTime(LocalDateTime.now().plusHours(DEFAULT_TTL_HOURS));
        
        base64Cache.put(cacheKey, result);
        log.debug("Base64已缓存: {}", imageFile.getName());
        
        // 清理过大的缓存
        if (base64Cache.size() > MAX_CACHE_SIZE) {
            cleanupOldestEntries(base64Cache, MAX_CACHE_SIZE / 2);
        }
        
        return base64Data;
    }
    
    /**
     * 获取或计算VLM识别结果
     */
    public String getOrComputeVlmResult(File imageFile, String prompt, String model) throws IOException {
        String cacheKey = generateVlmCacheKey(imageFile, prompt, model);
        
        CachedVlmResult cached = vlmResultCache.get(cacheKey);
        if (cached != null && !cached.isExpired()) {
            log.debug("VLM结果缓存命中: {} [{}]", imageFile.getName(), model);
            cached.incrementHitCount();
            return cached.getMarkdownResult();
        }
        
        log.debug("VLM结果缓存未命中: {} [{}]", imageFile.getName(), model);
        return null; // 表示需要重新计算
    }
    
    /**
     * 缓存VLM识别结果
     */
    public void cacheVlmResult(File imageFile, String prompt, String model, String markdownResult) throws IOException {
        String cacheKey = generateVlmCacheKey(imageFile, prompt, model);
        
        CachedVlmResult result = new CachedVlmResult();
        result.setFileHash(calculateFileHash(imageFile));
        result.setFileName(imageFile.getName());
        result.setPromptHash(calculateStringHash(prompt));
        result.setModel(model);
        result.setMarkdownResult(markdownResult);
        result.setCreationTime(LocalDateTime.now());
        result.setExpirationTime(LocalDateTime.now().plusHours(DEFAULT_TTL_HOURS));
        
        vlmResultCache.put(cacheKey, result);
        log.debug("VLM结果已缓存: {} [{}]", imageFile.getName(), model);
        
        // 清理过大的缓存
        if (vlmResultCache.size() > MAX_CACHE_SIZE) {
            cleanupOldestEntries(vlmResultCache, MAX_CACHE_SIZE / 2);
        }
    }
    
    /**
     * 检查图像是否已处理过
     */
    public boolean isImageProcessed(File imageFile, String prompt, String model) throws IOException {
        String cacheKey = generateVlmCacheKey(imageFile, prompt, model);
        CachedVlmResult cached = vlmResultCache.get(cacheKey);
        return cached != null && !cached.isExpired();
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        CacheStatistics stats = new CacheStatistics();
        
        // 计算各种缓存的统计信息
        stats.setBase64CacheSize(base64Cache.size());
        stats.setVlmResultCacheSize(vlmResultCache.size());
        stats.setImageCacheSize(imageCache.size());
        
        // 计算命中率
        long base64Hits = base64Cache.values().stream().mapToLong(CachedBase64Result::getHitCount).sum();
        long base64Total = base64Cache.values().stream().mapToLong(r -> r.getHitCount() + 1).sum();
        stats.setBase64HitRate(base64Total > 0 ? (double) base64Hits / base64Total * 100 : 0.0);
        
        long vlmHits = vlmResultCache.values().stream().mapToLong(CachedVlmResult::getHitCount).sum();
        long vlmTotal = vlmResultCache.values().stream().mapToLong(r -> r.getHitCount() + 1).sum();
        stats.setVlmHitRate(vlmTotal > 0 ? (double) vlmHits / vlmTotal * 100 : 0.0);
        
        // 计算缓存大小（估算）
        long base64Size = base64Cache.values().stream()
                .mapToLong(r -> r.getBase64Data() != null ? r.getBase64Data().length() : 0)
                .sum();
        stats.setBase64CacheMemoryUsage(base64Size);
        
        long vlmSize = vlmResultCache.values().stream()
                .mapToLong(r -> r.getMarkdownResult() != null ? r.getMarkdownResult().length() : 0)
                .sum();
        stats.setVlmCacheMemoryUsage(vlmSize);
        
        stats.setTotalMemoryUsage(base64Size + vlmSize);
        
        return stats;
    }
    
    /**
     * 清理所有缓存
     */
    public void clearAllCache() {
        imageCache.clear();
        base64Cache.clear();
        vlmResultCache.clear();
        log.info("所有缓存已清理");
    }
    
    /**
     * 清理过期缓存
     */
    public void cleanupExpiredCache() {
        LocalDateTime now = LocalDateTime.now();
        
        // 清理过期的Base64缓存
        int base64Removed = 0;
        base64Cache.entrySet().removeIf(entry -> {
            if (entry.getValue().isExpired()) {
                return true;
            }
            return false;
        });
        
        // 清理过期的VLM结果缓存
        int vlmRemoved = 0;
        vlmResultCache.entrySet().removeIf(entry -> {
            if (entry.getValue().isExpired()) {
                return true;
            }
            return false;
        });
        
        // 清理过期的图像缓存
        int imageRemoved = 0;
        imageCache.entrySet().removeIf(entry -> {
            if (entry.getValue().isExpired()) {
                return true;
            }
            return false;
        });
        
        if (base64Removed > 0 || vlmRemoved > 0 || imageRemoved > 0) {
            log.info("缓存清理完成 - Base64: {}, VLM: {}, Image: {}", base64Removed, vlmRemoved, imageRemoved);
        }
    }
    
    /**
     * 生成VLM缓存键
     */
    private String generateVlmCacheKey(File imageFile, String prompt, String model) throws IOException {
        String fileHash = calculateFileHash(imageFile);
        String promptHash = calculateStringHash(prompt);
        return String.format("vlm_%s_%s_%s", fileHash, promptHash, model);
    }
    
    /**
     * 计算文件哈希
     */
    private String calculateFileHash(File file) throws IOException {
        if (file.length() > MAX_FILE_SIZE) {
            // 对大文件只计算部分哈希
            return calculatePartialFileHash(file);
        }
        
        byte[] fileBytes = Files.readAllBytes(file.toPath());
        return calculateHash(fileBytes);
    }
    
    /**
     * 计算大文件的部分哈希
     */
    private String calculatePartialFileHash(File file) throws IOException {
        byte[] header = new byte[1024];
        byte[] footer = new byte[1024];
        
        try (var fileStream = Files.newInputStream(file.toPath())) {
            fileStream.read(header);
            
            long fileSize = file.length();
            if (fileSize > 1024) {
                fileStream.skip(fileSize - 2048);
                fileStream.read(footer);
            }
        }
        
        // 组合文件大小、头部和尾部的哈希
        String combined = file.length() + "_" + calculateHash(header) + "_" + calculateHash(footer);
        return calculateStringHash(combined);
    }
    
    /**
     * 计算字符串哈希
     */
    private String calculateStringHash(String input) {
        return calculateHash(input.getBytes());
    }
    
    /**
     * 计算字节数组哈希
     */
    private String calculateHash(byte[] input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(input);
            return Base64.getEncoder().encodeToString(hashBytes).substring(0, 16); // 使用前16位
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }
    
    /**
     * 清理最旧的缓存条目
     */
    private <T extends CachedResult> void cleanupOldestEntries(Map<String, T> cache, int targetSize) {
        if (cache.size() <= targetSize) {
            return;
        }
        
        // 按创建时间排序，删除最旧的条目
        cache.entrySet().stream()
                .sorted((e1, e2) -> e1.getValue().getCreationTime().compareTo(e2.getValue().getCreationTime()))
                .limit(cache.size() - targetSize)
                .map(Map.Entry::getKey)
                .forEach(cache::remove);
        
        log.debug("清理了 {} 个旧缓存条目，当前大小: {}", cache.size() - targetSize, cache.size());
    }
    
    /**
     * 缓存结果基类
     */
    public abstract static class CachedResult {
        protected LocalDateTime creationTime;
        protected LocalDateTime expirationTime;
        protected long hitCount = 0;
        
        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expirationTime);
        }
        
        public void incrementHitCount() {
            hitCount++;
        }
        
        // Getters and Setters
        public LocalDateTime getCreationTime() { return creationTime; }
        public void setCreationTime(LocalDateTime creationTime) { this.creationTime = creationTime; }
        
        public LocalDateTime getExpirationTime() { return expirationTime; }
        public void setExpirationTime(LocalDateTime expirationTime) { this.expirationTime = expirationTime; }
        
        public long getHitCount() { return hitCount; }
    }
    
    /**
     * 缓存的图像处理结果
     */
    public static class CachedImageResult extends CachedResult {
        private String fileHash;
        private String fileName;
        private long fileSize;
        private int originalWidth;
        private int originalHeight;
        private int processedWidth;
        private int processedHeight;
        
        // Getters and Setters
        public String getFileHash() { return fileHash; }
        public void setFileHash(String fileHash) { this.fileHash = fileHash; }
        
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public int getOriginalWidth() { return originalWidth; }
        public void setOriginalWidth(int originalWidth) { this.originalWidth = originalWidth; }
        
        public int getOriginalHeight() { return originalHeight; }
        public void setOriginalHeight(int originalHeight) { this.originalHeight = originalHeight; }
        
        public int getProcessedWidth() { return processedWidth; }
        public void setProcessedWidth(int processedWidth) { this.processedWidth = processedWidth; }
        
        public int getProcessedHeight() { return processedHeight; }
        public void setProcessedHeight(int processedHeight) { this.processedHeight = processedHeight; }
    }
    
    /**
     * 缓存的Base64结果
     */
    public static class CachedBase64Result extends CachedResult {
        private String fileHash;
        private String fileName;
        private long fileSize;
        private String base64Data;
        
        // Getters and Setters
        public String getFileHash() { return fileHash; }
        public void setFileHash(String fileHash) { this.fileHash = fileHash; }
        
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public String getBase64Data() { return base64Data; }
        public void setBase64Data(String base64Data) { this.base64Data = base64Data; }
    }
    
    /**
     * 缓存的VLM识别结果
     */
    public static class CachedVlmResult extends CachedResult {
        private String fileHash;
        private String fileName;
        private String promptHash;
        private String model;
        private String markdownResult;
        
        // Getters and Setters
        public String getFileHash() { return fileHash; }
        public void setFileHash(String fileHash) { this.fileHash = fileHash; }
        
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getPromptHash() { return promptHash; }
        public void setPromptHash(String promptHash) { this.promptHash = promptHash; }
        
        public String getModel() { return model; }
        public void setModel(String model) { this.model = model; }
        
        public String getMarkdownResult() { return markdownResult; }
        public void setMarkdownResult(String markdownResult) { this.markdownResult = markdownResult; }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStatistics {
        private int base64CacheSize;
        private int vlmResultCacheSize;
        private int imageCacheSize;
        private double base64HitRate;
        private double vlmHitRate;
        private long base64CacheMemoryUsage;
        private long vlmCacheMemoryUsage;
        private long totalMemoryUsage;
        
        // Getters and Setters
        public int getBase64CacheSize() { return base64CacheSize; }
        public void setBase64CacheSize(int base64CacheSize) { this.base64CacheSize = base64CacheSize; }
        
        public int getVlmResultCacheSize() { return vlmResultCacheSize; }
        public void setVlmResultCacheSize(int vlmResultCacheSize) { this.vlmResultCacheSize = vlmResultCacheSize; }
        
        public int getImageCacheSize() { return imageCacheSize; }
        public void setImageCacheSize(int imageCacheSize) { this.imageCacheSize = imageCacheSize; }
        
        public double getBase64HitRate() { return base64HitRate; }
        public void setBase64HitRate(double base64HitRate) { this.base64HitRate = base64HitRate; }
        
        public double getVlmHitRate() { return vlmHitRate; }
        public void setVlmHitRate(double vlmHitRate) { this.vlmHitRate = vlmHitRate; }
        
        public long getBase64CacheMemoryUsage() { return base64CacheMemoryUsage; }
        public void setBase64CacheMemoryUsage(long base64CacheMemoryUsage) { this.base64CacheMemoryUsage = base64CacheMemoryUsage; }
        
        public long getVlmCacheMemoryUsage() { return vlmCacheMemoryUsage; }
        public void setVlmCacheMemoryUsage(long vlmCacheMemoryUsage) { this.vlmCacheMemoryUsage = vlmCacheMemoryUsage; }
        
        public long getTotalMemoryUsage() { return totalMemoryUsage; }
        public void setTotalMemoryUsage(long totalMemoryUsage) { this.totalMemoryUsage = totalMemoryUsage; }
    }
}