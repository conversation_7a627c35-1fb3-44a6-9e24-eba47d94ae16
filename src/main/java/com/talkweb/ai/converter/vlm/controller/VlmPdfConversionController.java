package com.talkweb.ai.converter.vlm.controller;

import com.talkweb.ai.converter.vlm.cache.VlmCacheService;
import com.talkweb.ai.converter.vlm.config.VlmConfigurationManager;
import com.talkweb.ai.converter.vlm.model.VlmConversionRequest;
import com.talkweb.ai.converter.vlm.model.VlmConversionResult;
import com.talkweb.ai.converter.vlm.monitor.VlmPerformanceMonitor;
import com.talkweb.ai.converter.vlm.service.VlmBatchProcessingService;
import com.talkweb.ai.converter.vlm.service.VlmPdfConversionService;
import com.talkweb.ai.converter.vlm.service.impl.VlmPdfConversionServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * VLM PDF转换API控制器
 * 
 * 提供基于Vision Language Model的PDF文档转换REST API
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/vlm")
@Tag(name = "VLM PDF转换", description = "Vision Language Model PDF转换API")
public class VlmPdfConversionController {
    
    private static final Logger log = LoggerFactory.getLogger(VlmPdfConversionController.class);
    
    @Autowired
    private VlmPdfConversionService vlmPdfConversionService;
    
    @Autowired
    private VlmPdfConversionServiceImpl vlmPdfConversionServiceImpl;
    
    @Autowired
    private VlmBatchProcessingService batchProcessingService;
    
    @Autowired
    private VlmCacheService cacheService;
    
    @Autowired
    private VlmPerformanceMonitor performanceMonitor;
    
    @Autowired
    private VlmConfigurationManager configManager;
    
    /**
     * VLM PDF转换为Markdown
     */
    @PostMapping(value = "/convert", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "VLM PDF转换", description = "使用Vision Language Model将PDF转换为Markdown")
    public CompletableFuture<ResponseEntity<VlmConversionResult>> convertPdfToMarkdown(
            @Parameter(description = "PDF文件", required = true)
            @RequestParam("file") MultipartFile file,
            
            @Parameter(description = "VLM模型")
            @RequestParam(value = "vlmModel", defaultValue = "gpt-4-vision-preview") String vlmModel,
            
            @Parameter(description = "图像分辨率")
            @RequestParam(value = "dpi", defaultValue = "300") int dpi,
            
            @Parameter(description = "图像格式")
            @RequestParam(value = "imageFormat", defaultValue = "PNG") String imageFormat,
            
            @Parameter(description = "输出语言")
            @RequestParam(value = "language", defaultValue = "zh-CN") String language,
            
            @Parameter(description = "是否保持布局")
            @RequestParam(value = "preserveLayout", defaultValue = "true") boolean preserveLayout,
            
            @Parameter(description = "是否识别表格")
            @RequestParam(value = "recognizeTables", defaultValue = "true") boolean recognizeTables,
            
            @Parameter(description = "是否识别公式")
            @RequestParam(value = "recognizeFormulas", defaultValue = "true") boolean recognizeFormulas,
            
            @Parameter(description = "是否并行处理")
            @RequestParam(value = "parallelProcessing", defaultValue = "true") boolean parallelProcessing,
            
            @Parameter(description = "自定义提示词")
            @RequestParam(value = "customPrompt", required = false) String customPrompt) {
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Received VLM PDF conversion request: {}", file.getOriginalFilename());
                
                // 验证文件
                if (file.isEmpty()) {
                    return ResponseEntity.badRequest().body(
                            VlmConversionResult.failure("文件为空"));
                }
                
                if (!file.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
                    return ResponseEntity.badRequest().body(
                            VlmConversionResult.failure("只支持PDF文件"));
                }
                
                // 保存临时文件
                File tempFile = saveUploadedFile(file);
                
                try {
                    // 构建请求参数
                    VlmConversionRequest request = VlmConversionRequest.builder()
                            .vlmModel(vlmModel)
                            .dpi(dpi)
                            .imageFormat(imageFormat)
                            .language(language)
                            .preserveLayout(preserveLayout)
                            .recognizeTables(recognizeTables)
                            .recognizeFormulas(recognizeFormulas)
                            .enableParallelProcessing(parallelProcessing)
                            .customPrompt(customPrompt)
                            .build();
                    
                    // 执行转换
                    VlmConversionResult result = vlmPdfConversionService
                            .convertPdfToMarkdown(tempFile, request).get();
                    
                    if (result.isSuccess()) {
                        log.info("VLM PDF conversion completed successfully");
                        return ResponseEntity.ok(result);
                    } else {
                        log.error("VLM PDF conversion failed: {}", result.getErrorMessage());
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
                    }
                    
                } finally {
                    // 清理临时文件
                    if (tempFile.exists()) {
                        tempFile.delete();
                    }
                }
                
            } catch (Exception e) {
                log.error("VLM PDF conversion error: {}", e.getMessage(), e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(VlmConversionResult.failure("转换失败: " + e.getMessage()));
            }
        });
    }
    
    /**
     * 批量转换PDF文件
     */
    @PostMapping(value = "/batch-convert", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "批量VLM PDF转换", description = "批量将多个PDF文件转换为Markdown")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> batchConvertPdfs(
            @Parameter(description = "PDF文件列表", required = true)
            @RequestParam("files") MultipartFile[] files,
            
            @Parameter(description = "VLM模型")
            @RequestParam(value = "vlmModel", defaultValue = "gpt-4-vision-preview") String vlmModel,
            
            @Parameter(description = "图像分辨率")
            @RequestParam(value = "dpi", defaultValue = "300") int dpi) {
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("Received batch VLM PDF conversion request for {} files", files.length);
                
                List<File> tempFiles = new java.util.ArrayList<>();
                
                try {
                    // 保存所有上传文件
                    for (MultipartFile file : files) {
                        if (!file.isEmpty() && file.getOriginalFilename().toLowerCase().endsWith(".pdf")) {
                            tempFiles.add(saveUploadedFile(file));
                        }
                    }
                    
                    if (tempFiles.isEmpty()) {
                        return ResponseEntity.badRequest().body(
                                Map.of("success", false, "message", "没有有效的PDF文件"));
                    }
                    
                    // 构建请求参数
                    VlmConversionRequest request = VlmConversionRequest.builder()
                            .vlmModel(vlmModel)
                            .dpi(dpi)
                            .build();
                    
                    // 执行批量转换
                    List<VlmConversionResult> results = vlmPdfConversionServiceImpl
                            .batchConvertPdfs(tempFiles, request).get();
                    
                    long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
                    
                    Map<String, Object> response = new HashMap<>();
                    response.put("success", true);
                    response.put("totalFiles", results.size());
                    response.put("successfulConversions", successCount);
                    response.put("failedConversions", results.size() - successCount);
                    response.put("results", results);
                    
                    log.info("Batch conversion completed: {}/{} successful", successCount, results.size());
                    return ResponseEntity.ok(response);
                    
                } finally {
                    // 清理所有临时文件
                    tempFiles.forEach(file -> {
                        if (file.exists()) {
                            file.delete();
                        }
                    });
                }
                
            } catch (Exception e) {
                log.error("Batch VLM PDF conversion error: {}", e.getMessage(), e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("success", false, "message", "批量转换失败: " + e.getMessage()));
            }
        });
    }
    
    /**
     * 检查VLM服务可用性
     */
    @GetMapping("/health")
    @Operation(summary = "VLM服务健康检查", description = "检查VLM服务是否可用")
    public CompletableFuture<ResponseEntity<Map<String, Object>>> checkHealth() {
        return vlmPdfConversionService.checkVlmServiceAvailability()
                .thenApply(available -> {
                    Map<String, Object> response = new HashMap<>();
                    response.put("vlmServiceAvailable", available);
                    response.put("supportedModels", vlmPdfConversionService.getSupportedVlmModels());
                    response.put("statistics", vlmPdfConversionService.getConversionStatistics());
                    
                    return ResponseEntity.ok(response);
                })
                .exceptionally(ex -> {
                    log.error("Health check failed: {}", ex.getMessage());
                    return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                            .body(Map.of("vlmServiceAvailable", false, "error", ex.getMessage()));
                });
    }
    
    /**
     * 获取支持的VLM模型
     */
    @GetMapping("/models")
    @Operation(summary = "获取支持的VLM模型", description = "返回当前支持的VLM模型列表")
    public ResponseEntity<Map<String, Object>> getSupportedModels() {
        try {
            List<String> models = vlmPdfConversionService.getSupportedVlmModels();
            
            Map<String, Object> response = new HashMap<>();
            response.put("supportedModels", models);
            response.put("defaultModel", "gpt-4-vision-preview");
            response.put("count", models.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get supported models: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取模型列表失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取转换统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取转换统计", description = "获取VLM转换的统计信息")
    public ResponseEntity<VlmConversionResult.VlmStatistics> getStatistics() {
        try {
            VlmConversionResult.VlmStatistics statistics = vlmPdfConversionService.getConversionStatistics();
            return ResponseEntity.ok(statistics);
            
        } catch (Exception e) {
            log.error("Failed to get statistics: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 批量处理图像文件
     */
    @PostMapping(value = "/batch-images", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "批量图像处理", description = "批量处理图像文件并转换为Markdown")
    public CompletableFuture<ResponseEntity<VlmBatchProcessingService.BatchProcessingResult>> batchProcessImages(
            @Parameter(description = "图像文件列表", required = true)
            @RequestParam("files") MultipartFile[] files,
            
            @Parameter(description = "VLM模型")
            @RequestParam(value = "vlmModel", required = false) String vlmModel,
            
            @Parameter(description = "输出语言")
            @RequestParam(value = "language", defaultValue = "zh-CN") String language) {
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("收到批量图像处理请求，文件数量: {}", files.length);
                
                // 验证文件
                List<File> imageFiles = new java.util.ArrayList<>();
                for (MultipartFile file : files) {
                    if (!file.isEmpty() && isImageFile(file.getOriginalFilename())) {
                        imageFiles.add(saveUploadedFile(file));
                    }
                }
                
                if (imageFiles.isEmpty()) {
                    return ResponseEntity.badRequest().body(null);
                }
                
                // 构建请求参数
                VlmConversionRequest request = VlmConversionRequest.builder()
                        .vlmModel(vlmModel != null ? vlmModel : configManager.getRecommendedModel())
                        .language(language)
                        .build();
                
                // 执行批量处理
                VlmBatchProcessingService.BatchProcessingResult result = 
                        batchProcessingService.batchProcessImages(imageFiles, request).get();
                
                // 清理临时文件
                imageFiles.forEach(file -> {
                    if (file.exists()) file.delete();
                });
                
                return ResponseEntity.ok(result);
                
            } catch (Exception e) {
                log.error("批量图像处理失败: {}", e.getMessage(), e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
            }
        });
    }
    
    /**
     * 获取批量任务状态
     */
    @GetMapping("/batch-status/{batchId}")
    @Operation(summary = "获取批量任务状态", description = "获取批量处理任务的当前状态")
    public ResponseEntity<VlmBatchProcessingService.BatchTask> getBatchStatus(
            @Parameter(description = "批次ID", required = true)
            @PathVariable String batchId) {
        
        try {
            VlmBatchProcessingService.BatchTask task = batchProcessingService.getBatchTaskStatus(batchId);
            if (task != null) {
                return ResponseEntity.ok(task);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取批量任务状态失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取所有活跃批量任务
     */
    @GetMapping("/batch-tasks")
    @Operation(summary = "获取活跃批量任务", description = "获取所有当前活跃的批量处理任务")
    public ResponseEntity<List<VlmBatchProcessingService.BatchTask>> getActiveBatchTasks() {
        try {
            List<VlmBatchProcessingService.BatchTask> tasks = batchProcessingService.getActiveTasks();
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            log.error("获取活跃批量任务失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取性能监控报告
     */
    @GetMapping("/performance")
    @Operation(summary = "获取性能监控报告", description = "获取VLM服务的详细性能监控报告")
    public ResponseEntity<VlmPerformanceMonitor.VlmPerformanceReport> getPerformanceReport() {
        try {
            VlmPerformanceMonitor.VlmPerformanceReport report = performanceMonitor.generateReport();
            return ResponseEntity.ok(report);
        } catch (Exception e) {
            log.error("获取性能报告失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取性能监控摘要
     */
    @GetMapping("/performance/summary")
    @Operation(summary = "获取性能监控摘要", description = "获取VLM服务性能的简要摘要")
    public ResponseEntity<Map<String, Object>> getPerformanceSummary() {
        try {
            String summary = performanceMonitor.generateSummary();
            Map<String, Object> response = new HashMap<>();
            response.put("summary", summary);
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取性能摘要失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/statistics")
    @Operation(summary = "获取缓存统计", description = "获取VLM缓存系统的统计信息")
    public ResponseEntity<VlmCacheService.CacheStatistics> getCacheStatistics() {
        try {
            VlmCacheService.CacheStatistics stats = cacheService.getCacheStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取缓存统计失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 清理缓存
     */
    @DeleteMapping("/cache")
    @Operation(summary = "清理缓存", description = "清理VLM缓存系统中的所有缓存")
    public ResponseEntity<Map<String, Object>> clearCache() {
        try {
            cacheService.clearAllCache();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "缓存已清理");
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("清理缓存失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "message", "清理缓存失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取VLM配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取VLM配置", description = "获取VLM服务的配置信息和推荐设置")
    public ResponseEntity<VlmConfigurationManager.VlmConfigurationReport> getConfiguration() {
        try {
            VlmConfigurationManager.VlmConfigurationReport report = configManager.generateConfigurationReport();
            return ResponseEntity.ok(report);
        } catch (Exception e) {
            log.error("获取配置信息失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取配置指导
     */
    @GetMapping("/config/guide")
    @Operation(summary = "获取配置指导", description = "获取VLM配置的详细指导信息")
    public ResponseEntity<Map<String, Object>> getConfigurationGuide() {
        try {
            String guide = configManager.generateConfigurationGuide();
            Map<String, Object> response = new HashMap<>();
            response.put("guide", guide);
            response.put("availableProviders", configManager.getAvailableProviders());
            response.put("recommendedModel", configManager.getRecommendedModel());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取配置指导失败: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取转换进度
     */
    @GetMapping("/progress/{taskId}")
    @Operation(summary = "获取转换进度", description = "获取指定任务的转换进度")
    public CompletableFuture<ResponseEntity<VlmPdfConversionServiceImpl.ConversionProgress>> getProgress(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        
        return vlmPdfConversionServiceImpl.getConversionProgress(taskId)
                .thenApply(ResponseEntity::ok)
                .exceptionally(ex -> {
                    log.error("Failed to get progress for task {}: {}", taskId, ex.getMessage());
                    return ResponseEntity.notFound().build();
                });
    }
    
    /**
     * 检查是否为图像文件
     */
    private boolean isImageFile(String filename) {
        if (filename == null) return false;
        String ext = filename.toLowerCase();
        return ext.endsWith(".png") || ext.endsWith(".jpg") || ext.endsWith(".jpeg") || 
               ext.endsWith(".gif") || ext.endsWith(".bmp") || ext.endsWith(".webp");
    }
    
    /**
     * 保存上传的文件到临时目录
     */
    private File saveUploadedFile(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        String extension = fileName.substring(fileName.lastIndexOf('.'));
        String tempFileName = "vlm_pdf_" + UUID.randomUUID() + extension;
        
        Path tempDir = Files.createTempDirectory("vlm_conversion");
        Path tempFile = tempDir.resolve(tempFileName);
        
        Files.copy(file.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
        
        log.debug("Saved uploaded file to: {}", tempFile);
        return tempFile.toFile();
    }
    
    /**
     * 全局异常处理
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        log.error("Unhandled exception in VLM controller: {}", e.getMessage(), e);
        
        Map<String, Object> error = new HashMap<>();
        error.put("success", false);
        error.put("message", "服务器内部错误: " + e.getMessage());
        error.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}