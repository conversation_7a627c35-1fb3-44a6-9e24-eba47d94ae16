package com.talkweb.ai.converter.vlm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.MimeTypeUtils;

import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.util.Base64;
import java.util.Iterator;

/**
 * VLM图像处理工具类
 * 
 * 提供图像预处理、格式转换、质量优化等功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
public class VlmImageProcessor {
    
    private static final Logger log = LoggerFactory.getLogger(VlmImageProcessor.class);
    
    // 支持的图像格式
    private static final String[] SUPPORTED_FORMATS = {".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp"};
    
    // 默认图像质量设置
    private static final int MAX_WIDTH = 2048;
    private static final int MAX_HEIGHT = 2048;
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final int BUFFER_SIZE = 8192; // 8KB buffer for streaming
    private static final float JPEG_QUALITY = 0.85f; // JPEG compression quality
    
    /**
     * 处理图像文件，优化大小和格式
     */
    public static ProcessedImage processImage(File imageFile) throws IOException {
        log.debug("开始处理图像文件: {}", imageFile.getName());
        
        // 验证文件
        validateImageFile(imageFile);
        
        // 读取原始图像
        BufferedImage originalImage = ImageIO.read(imageFile);
        if (originalImage == null) {
            throw new IOException("无法读取图像文件: " + imageFile.getName());
        }
        
        // 获取原始信息
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        long originalSize = imageFile.length();
        
        log.debug("原始图像信息: {}x{}, {} KB", originalWidth, originalHeight, originalSize / 1024.0);
        
        // 检查是否需要调整大小
        BufferedImage processedImage = originalImage;
        boolean resized = false;
        
        if (originalWidth > MAX_WIDTH || originalHeight > MAX_HEIGHT) {
            processedImage = resizeImage(originalImage, MAX_WIDTH, MAX_HEIGHT);
            resized = true;
            log.debug("图像已调整大小至: {}x{}", processedImage.getWidth(), processedImage.getHeight());
        }
        
        // 转换为Base64
        String base64Data = imageToBase64(processedImage, getOptimalFormat(imageFile));
        String mimeType = determineMimeType(imageFile);
        
        ProcessedImage result = new ProcessedImage();
        result.setBase64Data(base64Data);
        result.setMimeType(mimeType);
        result.setOriginalWidth(originalWidth);
        result.setOriginalHeight(originalHeight);
        result.setProcessedWidth(processedImage.getWidth());
        result.setProcessedHeight(processedImage.getHeight());
        result.setOriginalSize(originalSize);
        result.setBase64Size(base64Data.length());
        result.setResized(resized);
        result.setCompressionRatio(calculateCompressionRatio(originalSize, base64Data.length()));
        
        log.debug("图像处理完成: Base64长度={}, 压缩比={:.2f}%", 
                 base64Data.length(), result.getCompressionRatio());
        
        return result;
    }
    
    /**
     * 直接将图像文件转换为Base64（简化版本）
     */
    public static String imageToBase64(File imageFile) throws IOException {
        validateImageFile(imageFile);
        return imageToBase64Streaming(imageFile);
    }
    
    /**
     * 使用流式处理将图像文件转换为Base64（内存友好）
     */
    public static String imageToBase64Streaming(File imageFile) throws IOException {
        validateImageFile(imageFile);
        
        try (FileInputStream fis = new FileInputStream(imageFile);
             BufferedInputStream bis = new BufferedInputStream(fis, BUFFER_SIZE);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            
            while ((bytesRead = bis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            
            return Base64.getEncoder().encodeToString(baos.toByteArray());
        }
    }
    
    /**
     * 将BufferedImage转换为Base64字符串（带压缩优化）
     */
    private static String imageToBase64(BufferedImage image, String format) throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            
            if ("jpg".equals(format) || "jpeg".equals(format)) {
                // 对JPEG使用质量压缩
                writeJpegWithQuality(image, baos, JPEG_QUALITY);
            } else {
                // 其他格式使用标准写入
                ImageIO.write(image, format, baos);
            }
            
            return Base64.getEncoder().encodeToString(baos.toByteArray());
        }
    }
    
    /**
     * 以指定质量写入JPEG图像
     */
    private static void writeJpegWithQuality(BufferedImage image, ByteArrayOutputStream baos, float quality) throws IOException {
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
        if (!writers.hasNext()) {
            throw new IOException("No JPEG writers available");
        }
        
        ImageWriter writer = writers.next();
        ImageWriteParam param = writer.getDefaultWriteParam();
        
        if (param.canWriteCompressed()) {
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(quality);
        }
        
        try (ImageOutputStream ios = ImageIO.createImageOutputStream(baos)) {
            writer.setOutput(ios);
            writer.write(null, new javax.imageio.IIOImage(image, null, null), param);
        } finally {
            writer.dispose();
        }
    }
    
    /**
     * 调整图像大小（保持宽高比）
     */
    private static BufferedImage resizeImage(BufferedImage originalImage, int maxWidth, int maxHeight) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        
        // 计算新的尺寸（保持宽高比）
        double scale = Math.min((double) maxWidth / originalWidth, (double) maxHeight / originalHeight);
        int newWidth = (int) (originalWidth * scale);
        int newHeight = (int) (originalHeight * scale);
        
        // 创建新图像
        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();
        
        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 绘制调整后的图像
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        return resizedImage;
    }
    
    /**
     * 验证图像文件
     */
    private static void validateImageFile(File imageFile) throws IOException {
        if (!imageFile.exists()) {
            throw new FileNotFoundException("图像文件不存在: " + imageFile.getPath());
        }
        
        if (!imageFile.canRead()) {
            throw new IOException("无法读取图像文件: " + imageFile.getPath());
        }
        
        if (imageFile.length() > MAX_FILE_SIZE) {
            throw new IOException("图像文件过大: " + imageFile.length() + " bytes (最大: " + MAX_FILE_SIZE + ")");
        }
        
        String fileName = imageFile.getName().toLowerCase();
        boolean supportedFormat = false;
        for (String format : SUPPORTED_FORMATS) {
            if (fileName.endsWith(format)) {
                supportedFormat = true;
                break;
            }
        }
        
        if (!supportedFormat) {
            throw new IOException("不支持的图像格式: " + fileName);
        }
    }
    
    /**
     * 确定MIME类型
     */
    private static String determineMimeType(File imageFile) throws IOException {
        String fileName = imageFile.getName().toLowerCase();
        if (fileName.endsWith(".png")) {
            return MimeTypeUtils.IMAGE_PNG_VALUE;
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return MimeTypeUtils.IMAGE_JPEG_VALUE;
        } else if (fileName.endsWith(".gif")) {
            return MimeTypeUtils.IMAGE_GIF_VALUE;
        } else if (fileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (fileName.endsWith(".webp")) {
            return "image/webp";
        } else {
            // 使用系统方法检测
            String mimeType = Files.probeContentType(imageFile.toPath());
            if (mimeType != null && mimeType.startsWith("image/")) {
                return mimeType;
            } else {
                throw new IOException("无法确定MIME类型: " + imageFile.getName());
            }
        }
    }
    
    /**
     * 获取最优输出格式
     */
    private static String getOptimalFormat(File imageFile) {
        String fileName = imageFile.getName().toLowerCase();
        if (fileName.endsWith(".png") || fileName.endsWith(".gif")) {
            return "png"; // 保持透明度
        } else {
            return "jpg"; // 更小的文件大小
        }
    }
    
    /**
     * 计算压缩比
     */
    private static double calculateCompressionRatio(long originalSize, int base64Size) {
        // Base64编码会增加约33%的大小，所以需要调整计算
        long estimatedOriginalFromBase64 = (long) (base64Size * 0.75);
        return ((double) estimatedOriginalFromBase64 / originalSize) * 100;
    }
    
    /**
     * 创建图像处理统计信息
     */
    public static ImageProcessingStats createStats(ProcessedImage processedImage) {
        ImageProcessingStats stats = new ImageProcessingStats();
        stats.setOriginalDimensions(processedImage.getOriginalWidth() + "x" + processedImage.getOriginalHeight());
        stats.setProcessedDimensions(processedImage.getProcessedWidth() + "x" + processedImage.getProcessedHeight());
        stats.setOriginalSize(processedImage.getOriginalSize());
        stats.setBase64Size(processedImage.getBase64Size());
        stats.setCompressionRatio(processedImage.getCompressionRatio());
        stats.setResized(processedImage.isResized());
        stats.setMimeType(processedImage.getMimeType());
        return stats;
    }
    
    /**
     * 处理后的图像信息
     */
    public static class ProcessedImage {
        private String base64Data;
        private String mimeType;
        private int originalWidth;
        private int originalHeight;
        private int processedWidth;
        private int processedHeight;
        private long originalSize;
        private int base64Size;
        private boolean resized;
        private double compressionRatio;
        
        // Getters and Setters
        public String getBase64Data() { return base64Data; }
        public void setBase64Data(String base64Data) { this.base64Data = base64Data; }
        
        public String getMimeType() { return mimeType; }
        public void setMimeType(String mimeType) { this.mimeType = mimeType; }
        
        public int getOriginalWidth() { return originalWidth; }
        public void setOriginalWidth(int originalWidth) { this.originalWidth = originalWidth; }
        
        public int getOriginalHeight() { return originalHeight; }
        public void setOriginalHeight(int originalHeight) { this.originalHeight = originalHeight; }
        
        public int getProcessedWidth() { return processedWidth; }
        public void setProcessedWidth(int processedWidth) { this.processedWidth = processedWidth; }
        
        public int getProcessedHeight() { return processedHeight; }
        public void setProcessedHeight(int processedHeight) { this.processedHeight = processedHeight; }
        
        public long getOriginalSize() { return originalSize; }
        public void setOriginalSize(long originalSize) { this.originalSize = originalSize; }
        
        public int getBase64Size() { return base64Size; }
        public void setBase64Size(int base64Size) { this.base64Size = base64Size; }
        
        public boolean isResized() { return resized; }
        public void setResized(boolean resized) { this.resized = resized; }
        
        public double getCompressionRatio() { return compressionRatio; }
        public void setCompressionRatio(double compressionRatio) { this.compressionRatio = compressionRatio; }
    }
    
    /**
     * 图像处理统计信息
     */
    public static class ImageProcessingStats {
        private String originalDimensions;
        private String processedDimensions;
        private long originalSize;
        private int base64Size;
        private double compressionRatio;
        private boolean resized;
        private String mimeType;
        
        // Getters and Setters
        public String getOriginalDimensions() { return originalDimensions; }
        public void setOriginalDimensions(String originalDimensions) { this.originalDimensions = originalDimensions; }
        
        public String getProcessedDimensions() { return processedDimensions; }
        public void setProcessedDimensions(String processedDimensions) { this.processedDimensions = processedDimensions; }
        
        public long getOriginalSize() { return originalSize; }
        public void setOriginalSize(long originalSize) { this.originalSize = originalSize; }
        
        public int getBase64Size() { return base64Size; }
        public void setBase64Size(int base64Size) { this.base64Size = base64Size; }
        
        public double getCompressionRatio() { return compressionRatio; }
        public void setCompressionRatio(double compressionRatio) { this.compressionRatio = compressionRatio; }
        
        public boolean isResized() { return resized; }
        public void setResized(boolean resized) { this.resized = resized; }
        
        public String getMimeType() { return mimeType; }
        public void setMimeType(String mimeType) { this.mimeType = mimeType; }
    }
}