package com.talkweb.ai.converter.vlm.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * VLM配置管理器
 * 
 * 动态管理和检测VLM服务提供商配置
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Component
public class VlmConfigurationManager {
    
    private static final Logger log = LoggerFactory.getLogger(VlmConfigurationManager.class);
    
    @Value("${vlm.default-model:qwen3-14b}")
    private String defaultModel;
    
    @Value("${vlm.default-language:zh-CN}")
    private String defaultLanguage;
    
    @Value("${vlm.max-retries:3}")
    private int maxRetries;
    
    @Value("${vlm.timeout-seconds:30}")
    private int timeoutSeconds;
    
    // VLM服务提供商配置
    private final Map<String, VlmProvider> providers = new LinkedHashMap<>();
    
    // 模型到提供商的映射
    private final Map<String, String> modelToProvider = new HashMap<>();
    
    @PostConstruct
    public void initialize() {
        setupProviders();
        detectAvailableProviders();
        log.info("VLM配置管理器初始化完成，检测到 {} 个可用提供商", getAvailableProviders().size());
    }
    
    /**
     * 设置VLM服务提供商
     */
    private void setupProviders() {
        // 国产大模型提供商
        addProvider("qwen", "通义千问", 
                   Arrays.asList("qwen3-14b", "qwen-vl-plus", "qwen-vl-max"),
                   "QWEN_API_KEY", 
                   "https://dashscope.aliyuncs.com/compatible-mode/v1",
                   "阿里云通义千问VLM系列，支持中文文档识别");
        
        addProvider("zhipu", "智谱AI", 
                   Arrays.asList("chatglm-6b", "glm-4v"),
                   "ZHIPU_API_KEY",
                   "https://open.bigmodel.cn/api/paas/v4",
                   "智谱AI GLM系列视觉模型");
        
        addProvider("baichuan", "百川AI", 
                   Arrays.asList("baichuan2-13b"),
                   "BAICHUAN_API_KEY",
                   "https://api.baichuan-ai.com/v1",
                   "百川AI大模型视觉版本");
        
        addProvider("internlm", "书生·浦语", 
                   Arrays.asList("internlm-xcomposer2-7b"),
                   "INTERNLM_API_KEY",
                   "https://internlm-chat.intern-ai.org.cn/puyu/api/v1",
                   "上海AI实验室书生·浦语多模态模型");
        
        // 国际模型提供商
        addProvider("openai", "OpenAI", 
                   Arrays.asList("gpt-4-vision-preview", "gpt-4o", "gpt-4o-mini"),
                   "OPENAI_API_KEY",
                   "https://api.openai.com/v1",
                   "OpenAI GPT-4 Vision系列模型");
        
        addProvider("anthropic", "Anthropic", 
                   Arrays.asList("claude-3-opus", "claude-3-sonnet", "claude-3-haiku"),
                   "ANTHROPIC_API_KEY",
                   "https://api.anthropic.com/v1",
                   "Anthropic Claude 3系列视觉模型");
    }
    
    /**
     * 添加VLM提供商
     */
    private void addProvider(String id, String name, List<String> models, 
                           String apiKeyEnv, String baseUrl, String description) {
        VlmProvider provider = new VlmProvider();
        provider.setId(id);
        provider.setName(name);
        provider.setSupportedModels(models);
        provider.setApiKeyEnvironmentVariable(apiKeyEnv);
        provider.setBaseUrl(baseUrl);
        provider.setDescription(description);
        provider.setDomestic(isDomesticProvider(id));
        
        providers.put(id, provider);
        
        // 建立模型到提供商的映射
        for (String model : models) {
            modelToProvider.put(model, id);
        }
    }
    
    /**
     * 检测可用的VLM提供商
     */
    private void detectAvailableProviders() {
        for (VlmProvider provider : providers.values()) {
            String apiKey = System.getenv(provider.getApiKeyEnvironmentVariable());
            boolean available = apiKey != null && !apiKey.trim().isEmpty();
            provider.setAvailable(available);
            
            if (available) {
                log.info("✅ 检测到可用提供商: {} ({})", provider.getName(), provider.getId());
            } else {
                log.debug("⚠️ 提供商不可用: {} - 未找到环境变量 {}", 
                         provider.getName(), provider.getApiKeyEnvironmentVariable());
            }
        }
    }
    
    /**
     * 判断是否为国产提供商
     */
    private boolean isDomesticProvider(String providerId) {
        return Arrays.asList("qwen", "zhipu", "baichuan", "internlm").contains(providerId);
    }
    
    /**
     * 获取所有可用的提供商
     */
    public List<VlmProvider> getAvailableProviders() {
        return providers.values().stream()
                .filter(VlmProvider::isAvailable)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有国产提供商（不管是否可用）
     */
    public List<VlmProvider> getDomesticProviders() {
        return providers.values().stream()
                .filter(VlmProvider::isDomestic)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有国际提供商（不管是否可用）
     */
    public List<VlmProvider> getInternationalProviders() {
        return providers.values().stream()
                .filter(provider -> !provider.isDomestic())
                .collect(Collectors.toList());
    }
    
    /**
     * 根据模型名称获取提供商
     */
    public VlmProvider getProviderForModel(String modelName) {
        String providerId = modelToProvider.get(modelName);
        return providerId != null ? providers.get(providerId) : null;
    }
    
    /**
     * 获取推荐的模型（优先国产，然后可用性）
     */
    public String getRecommendedModel() {
        // 1. 优先推荐可用的国产模型
        List<VlmProvider> availableDomestic = getAvailableProviders().stream()
                .filter(VlmProvider::isDomestic)
                .collect(Collectors.toList());
        
        if (!availableDomestic.isEmpty()) {
            // 优先推荐通义千问
            VlmProvider qwen = availableDomestic.stream()
                    .filter(p -> "qwen".equals(p.getId()))
                    .findFirst()
                    .orElse(availableDomestic.get(0));
            return qwen.getSupportedModels().get(0);
        }
        
        // 2. 如果没有可用的国产模型，使用国际模型
        List<VlmProvider> availableInternational = getAvailableProviders().stream()
                .filter(provider -> !provider.isDomestic())
                .collect(Collectors.toList());
        
        if (!availableInternational.isEmpty()) {
            return availableInternational.get(0).getSupportedModels().get(0);
        }
        
        // 3. 如果都不可用，返回默认模型
        return defaultModel;
    }
    
    /**
     * 获取所有支持的模型列表
     */
    public List<String> getAllSupportedModels() {
        return providers.values().stream()
                .flatMap(provider -> provider.getSupportedModels().stream())
                .collect(Collectors.toList());
    }
    
    /**
     * 生成配置报告
     */
    public VlmConfigurationReport generateConfigurationReport() {
        VlmConfigurationReport report = new VlmConfigurationReport();
        report.setDefaultModel(defaultModel);
        report.setDefaultLanguage(defaultLanguage);
        report.setMaxRetries(maxRetries);
        report.setTimeoutSeconds(timeoutSeconds);
        report.setRecommendedModel(getRecommendedModel());
        
        report.setTotalProviders(providers.size());
        report.setAvailableProviders(getAvailableProviders().size());
        report.setDomesticProviders(getDomesticProviders().size());
        report.setInternationalProviders(getInternationalProviders().size());
        
        report.setTotalModels(getAllSupportedModels().size());
        report.setAvailableModels(getAvailableProviders().stream()
                .mapToInt(p -> p.getSupportedModels().size())
                .sum());
        
        report.setProviderDetails(new ArrayList<>(providers.values()));
        
        return report;
    }
    
    /**
     * 生成配置指导信息
     */
    public String generateConfigurationGuide() {
        StringBuilder guide = new StringBuilder();
        guide.append("# VLM模型配置指南\n\n");
        
        List<VlmProvider> unavailableProviders = providers.values().stream()
                .filter(provider -> !provider.isAvailable())
                .collect(Collectors.toList());
        
        if (unavailableProviders.isEmpty()) {
            guide.append("✅ **所有VLM提供商都已正确配置！**\n\n");
        } else {
            guide.append("## 🔧 需要配置的提供商\n\n");
            
            // 按国产/国际分组
            Map<Boolean, List<VlmProvider>> grouped = unavailableProviders.stream()
                    .collect(Collectors.groupingBy(VlmProvider::isDomestic));
            
            if (grouped.containsKey(true)) {
                guide.append("### 🇨🇳 国产大模型\n\n");
                for (VlmProvider provider : grouped.get(true)) {
                    appendProviderConfig(guide, provider);
                }
            }
            
            if (grouped.containsKey(false)) {
                guide.append("### 🌐 国际模型\n\n");
                for (VlmProvider provider : grouped.get(false)) {
                    appendProviderConfig(guide, provider);
                }
            }
        }
        
        guide.append("## 📊 当前状态\n\n");
        guide.append("- 可用提供商: ").append(getAvailableProviders().size()).append("/").append(providers.size()).append("\n");
        guide.append("- 推荐模型: ").append(getRecommendedModel()).append("\n");
        guide.append("- 默认语言: ").append(defaultLanguage).append("\n");
        
        return guide.toString();
    }
    
    /**
     * 添加提供商配置信息到指南
     */
    private void appendProviderConfig(StringBuilder guide, VlmProvider provider) {
        guide.append("**").append(provider.getName()).append("**\n");
        guide.append("```bash\n");
        guide.append("export ").append(provider.getApiKeyEnvironmentVariable()).append("=\"your-api-key\"\n");
        guide.append("```\n");
        guide.append("- 支持模型: ").append(String.join(", ", provider.getSupportedModels())).append("\n");
        guide.append("- 说明: ").append(provider.getDescription()).append("\n\n");
    }
    
    // Getters
    public String getDefaultModel() { return defaultModel; }
    public String getDefaultLanguage() { return defaultLanguage; }
    public int getMaxRetries() { return maxRetries; }
    public int getTimeoutSeconds() { return timeoutSeconds; }
    public Map<String, VlmProvider> getProviders() { return new HashMap<>(providers); }
    
    /**
     * VLM服务提供商信息
     */
    public static class VlmProvider {
        private String id;
        private String name;
        private List<String> supportedModels;
        private String apiKeyEnvironmentVariable;
        private String baseUrl;
        private String description;
        private boolean domestic;
        private boolean available;
        
        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public List<String> getSupportedModels() { return supportedModels; }
        public void setSupportedModels(List<String> supportedModels) { this.supportedModels = supportedModels; }
        
        public String getApiKeyEnvironmentVariable() { return apiKeyEnvironmentVariable; }
        public void setApiKeyEnvironmentVariable(String apiKeyEnvironmentVariable) { this.apiKeyEnvironmentVariable = apiKeyEnvironmentVariable; }
        
        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public boolean isDomestic() { return domestic; }
        public void setDomestic(boolean domestic) { this.domestic = domestic; }
        
        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }
    }
    
    /**
     * VLM配置报告
     */
    public static class VlmConfigurationReport {
        private String defaultModel;
        private String defaultLanguage;
        private int maxRetries;
        private int timeoutSeconds;
        private String recommendedModel;
        
        private int totalProviders;
        private int availableProviders;
        private int domesticProviders;
        private int internationalProviders;
        
        private int totalModels;
        private int availableModels;
        
        private List<VlmProvider> providerDetails;
        
        // Getters and Setters
        public String getDefaultModel() { return defaultModel; }
        public void setDefaultModel(String defaultModel) { this.defaultModel = defaultModel; }
        
        public String getDefaultLanguage() { return defaultLanguage; }
        public void setDefaultLanguage(String defaultLanguage) { this.defaultLanguage = defaultLanguage; }
        
        public int getMaxRetries() { return maxRetries; }
        public void setMaxRetries(int maxRetries) { this.maxRetries = maxRetries; }
        
        public int getTimeoutSeconds() { return timeoutSeconds; }
        public void setTimeoutSeconds(int timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }
        
        public String getRecommendedModel() { return recommendedModel; }
        public void setRecommendedModel(String recommendedModel) { this.recommendedModel = recommendedModel; }
        
        public int getTotalProviders() { return totalProviders; }
        public void setTotalProviders(int totalProviders) { this.totalProviders = totalProviders; }
        
        public int getAvailableProviders() { return availableProviders; }
        public void setAvailableProviders(int availableProviders) { this.availableProviders = availableProviders; }
        
        public int getDomesticProviders() { return domesticProviders; }
        public void setDomesticProviders(int domesticProviders) { this.domesticProviders = domesticProviders; }
        
        public int getInternationalProviders() { return internationalProviders; }
        public void setInternationalProviders(int internationalProviders) { this.internationalProviders = internationalProviders; }
        
        public int getTotalModels() { return totalModels; }
        public void setTotalModels(int totalModels) { this.totalModels = totalModels; }
        
        public int getAvailableModels() { return availableModels; }
        public void setAvailableModels(int availableModels) { this.availableModels = availableModels; }
        
        public List<VlmProvider> getProviderDetails() { return providerDetails; }
        public void setProviderDetails(List<VlmProvider> providerDetails) { this.providerDetails = providerDetails; }
    }
}