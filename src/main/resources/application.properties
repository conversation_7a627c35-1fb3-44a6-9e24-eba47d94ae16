# Logging configuration
logging.level.root=INFO
logging.level.com.talkweb.ai.converter=DEBUG
logging.level.org.springframework.ai=DEBUG

# Spring application name
spring.application.name=ai-indexer

# AI Configuration
ai.enabled=true

# Spring AI OpenAI Configuration - å®å¨éç½®ï¼ä¸æä¾é»è®¤å¼
spring.ai.openai.api-key=${OPENAI_API_KEY:}
spring.ai.openai.base-url=https://api.openai.com
spring.ai.openai.chat.options.model=gpt-3.5-turbo
spring.ai.openai.chat.options.temperature=0.7
spring.ai.openai.chat.options.max-tokens=2000
spring.ai.openai.embedding.options.model=text-embedding-ada-002

# Spring AI MCP Server Configuration
# Enable MCP server with WebMVC transport
spring.ai.mcp.server.enabled=true

# CORSå®å¨éç½®
app.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000,http://127.0.0.1:8080}
app.cors.allowed-headers=Content-Type,Authorization,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allow-credentials=${CORS_ALLOW_CREDENTIALS:false}
app.cors.max-age=${CORS_MAX_AGE:3600}

# æä»¶å®å¨éç½®
plugin.security.enabled=${PLUGIN_SECURITY_ENABLED:true}
plugin.security.require-signature=${PLUGIN_REQUIRE_SIGNATURE:true}
plugin.security.trusted-certs-path=${PLUGIN_TRUSTED_CERTS_PATH:}
plugin.security.whitelist.enabled=${PLUGIN_WHITELIST_ENABLED:true}
plugin.security.whitelist.plugins=${PLUGIN_WHITELIST_PLUGINS:}
plugin.security.whitelist.developers=${PLUGIN_WHITELIST_DEVELOPERS:TalkWeb,AI Converter Team}