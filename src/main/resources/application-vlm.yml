# VLM (Vision Language Model) 配置
vlm:
  # 是否启用VLM功能
  enabled: true
  
  # 默认配置（优先使用国产大模型）
  default-model: qwen3-14b
  default-dpi: 300
  default-image-format: PNG
  default-language: zh-CN
  
  # 性能配置
  performance:
    enable-parallel-processing: true
    max-concurrent-tasks: 5
    timeout-ms: 120000  # 2分钟
    thread-pool-size: 10
  
  # 质量控制配置
  quality-control:
    confidence-threshold: 0.8
    enable-post-processing: true
    enable-structure-analysis: true
    enable-table-recognition: true
    enable-formula-recognition: true
    preserve-layout: true
  
  # 模型配置
  model-config:
    supported-models:
      # OpenAI模型
      - gpt-4-vision-preview
      - gpt-4o
      - gpt-4o-mini
      # Anthropic模型
      - claude-3-opus
      - claude-3-sonnet
      - claude-3-haiku
      # 国产大模型
      - qwen3-14b
      - qwen-vl-plus
      - qwen-vl-max
      - chatglm-6b
      - baichuan2-13b
      - internlm-xcomposer2-7b
    
    model-temperatures:
      # OpenAI模型
      gpt-4-vision-preview: 0.1
      gpt-4o: 0.1
      gpt-4o-mini: 0.1
      # Anthropic模型
      claude-3-opus: 0.1
      claude-3-sonnet: 0.1
      claude-3-haiku: 0.1
      # 国产大模型
      qwen3-14b: 0.1
      qwen-vl-plus: 0.1
      qwen-vl-max: 0.1
      chatglm-6b: 0.1
      baichuan2-13b: 0.1
      internlm-xcomposer2-7b: 0.1
    
    model-max-tokens:
      # OpenAI模型
      gpt-4-vision-preview: 4096
      gpt-4o: 8192
      gpt-4o-mini: 4096
      # Anthropic模型
      claude-3-opus: 4096
      claude-3-sonnet: 4096
      claude-3-haiku: 4096
      # 国产大模型
      qwen3-14b: 8192
      qwen-vl-plus: 8192
      qwen-vl-max: 8192
      chatglm-6b: 4096
      baichuan2-13b: 4096
      internlm-xcomposer2-7b: 4096
  
  # 文件限制配置
  file-limits:
    max-file-size-bytes: 104857600  # 100MB
    max-image-size-bytes: 10485760  # 10MB
    max-pages-per-pdf: 100
    max-batch-size: 10
    supported-image-formats:
      - PNG
      - JPEG
      - JPG
    min-dpi: 72
    max-dpi: 600
  
  # 重试配置
  retry-config:
    max-retries: 3
    retry-delay-ms: 1000
    retry-multiplier: 2.0
    max-retry-delay-ms: 30000

# Spring AI配置（用于VLM集成）
spring:
  ai:
    # OpenAI配置
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      chat:
        options:
          model: gpt-4-vision-preview
          temperature: 0.1
          max-tokens: 4096
    
    # Anthropic配置
    anthropic:
      api-key: ${ANTHROPIC_API_KEY:your-anthropic-api-key}
      chat:
        options:
          model: claude-3-opus-20240229
          temperature: 0.1
          max-tokens: 4096
    
    # 通义千问配置
    qwen:
      api-key: ${QWEN_API_KEY:your-qwen-api-key}
      base-url: ${QWEN_BASE_URL:https://dashscope.aliyuncs.com/compatible-mode/v1}
      chat:
        options:
          model: qwen3-14b
          temperature: 0.1
          max-tokens: 8192
    
    # 智谱AI配置
    zhipu:
      api-key: ${ZHIPU_API_KEY:your-zhipu-api-key}
      base-url: ${ZHIPU_BASE_URL:https://open.bigmodel.cn/api/paas/v4}
      chat:
        options:
          model: chatglm-6b
          temperature: 0.1
          max-tokens: 4096
    
    # 百川AI配置
    baichuan:
      api-key: ${BAICHUAN_API_KEY:your-baichuan-api-key}
      base-url: ${BAICHUAN_BASE_URL:https://api.baichuan-ai.com/v1}
      chat:
        options:
          model: baichuan2-13b
          temperature: 0.1
          max-tokens: 4096

# 日志配置
logging:
  level:
    com.talkweb.ai.converter.vlm: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
  file:
    name: logs/vlm-conversion.log
    max-size: 100MB
    max-history: 30