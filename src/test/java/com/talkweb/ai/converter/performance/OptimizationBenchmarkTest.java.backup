package com.talkweb.ai.converter.performance;

import com.talkweb.ai.converter.core.impl.ExcelToMarkdownConverter;
import com.talkweb.ai.converter.vlm.util.VlmImageProcessor;
import com.talkweb.ai.converter.core.exception.PerformanceOptimizedExceptionHandler;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.util.StopWatch;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 优化功能性能基准测试
 * 
 * 测试各种优化功能的性能指标和基准数据
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@DisplayName("优化功能性能基准测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class OptimizationBenchmarkTest {

    @TempDir
    static Path tempDir;
    
    private static File testImage;
    private static File largeImage;
    private static final List<BenchmarkResult> results = new ArrayList<>();

    @BeforeAll
    static void setUpClass() throws IOException {
        // 创建测试图像
        testImage = createTestImage(tempDir, "benchmark-test.png", 1024, 768);
        largeImage = createTestImage(tempDir, "benchmark-large.jpg", 2560, 1920);
    }

    @AfterAll
    static void tearDownClass() {
        // 输出所有基准测试结果
        System.out.println("\n==================== 性能基准测试报告 ====================");
        for (BenchmarkResult result : results) {
            System.out.println(result);
        }
        System.out.println("========================================================");
    }

    @BeforeEach
    void setUp() {
        // 清理缓存以确保测试独立性
        ExcelToMarkdownConverter.clearCache();
        PerformanceOptimizedExceptionHandler.resetStatistics();
    }

    private static File createTestImage(Path directory, String filename, int width, int height) throws IOException {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.BLUE);
        g2d.fillRect(0, 0, width, height);
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("Arial", Font.BOLD, 24));
        g2d.drawString("Benchmark Test " + width + "x" + height, 50, 50);
        g2d.dispose();
        
        File imageFile = directory.resolve(filename).toFile();
        String format = filename.endsWith(".png") ? "png" : "jpg";
        ImageIO.write(image, format, imageFile);
        
        return imageFile;
    }

    @Test
    @Order(1)
    @DisplayName("Excel缓存性能基准测试")
    void benchmarkExcelCachePerformance() {
        System.out.println("开始Excel缓存性能基准测试...");
        
        // 基准1：缓存统计获取性能
        StopWatch stopWatch = new StopWatch("Excel缓存统计性能");
        
        stopWatch.start("热身阶段");
        for (int i = 0; i < 100; i++) {
            ExcelToMarkdownConverter.getCacheStatistics();
        }
        stopWatch.stop();
        
        stopWatch.start("主测试阶段");
        int iterations = 10000;
        for (int i = 0; i < iterations; i++) {
            Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
            assertNotNull(stats);
        }
        stopWatch.stop();
        
        double avgTimePerCall = stopWatch.getLastTaskTimeMillis() / (double) iterations;
        results.add(new BenchmarkResult(
            "Excel缓存统计获取",
            iterations,
            stopWatch.getLastTaskTimeMillis(),
            avgTimePerCall,
            "次/ms"
        ));
        
        // 性能断言
        assertTrue(avgTimePerCall < 0.1, "Excel缓存统计平均调用时间应小于0.1ms，实际: " + avgTimePerCall + "ms");
        assertTrue(stopWatch.getLastTaskTimeMillis() < 1000, "10000次调用应在1秒内完成");
        
        System.out.println("Excel缓存统计性能: " + avgTimePerCall + "ms/次");
    }

    @Test
    @Order(2)
    @DisplayName("VLM图像处理性能基准测试")
    void benchmarkVlmImageProcessingPerformance() throws IOException {
        System.out.println("开始VLM图像处理性能基准测试...");
        
        // 基准1：普通图像处理
        StopWatch stopWatch = new StopWatch("VLM图像处理性能");
        
        stopWatch.start("普通图像处理（无调整大小）");
        int normalIterations = 50;
        for (int i = 0; i < normalIterations; i++) {
            VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(testImage);
            assertNotNull(result);
            assertFalse(result.isResized());
        }
        stopWatch.stop();
        
        double normalAvgTime = stopWatch.getLastTaskTimeMillis() / (double) normalIterations;
        results.add(new BenchmarkResult(
            "VLM普通图像处理",
            normalIterations,
            stopWatch.getLastTaskTimeMillis(),
            normalAvgTime,
            "张/ms"
        ));
        
        // 基准2：大图像处理（需要调整大小）
        stopWatch.start("大图像处理（需调整大小）");
        int largeIterations = 20;
        for (int i = 0; i < largeIterations; i++) {
            VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(largeImage);
            assertNotNull(result);
            assertTrue(result.isResized());
            assertTrue(result.getProcessedWidth() <= 2048);
            assertTrue(result.getProcessedHeight() <= 2048);
        }
        stopWatch.stop();
        
        double largeAvgTime = stopWatch.getLastTaskTimeMillis() / (double) largeIterations;
        results.add(new BenchmarkResult(
            "VLM大图像处理",
            largeIterations,
            stopWatch.getLastTaskTimeMillis(),
            largeAvgTime,
            "张/ms"
        ));
        
        // 基准3：Base64编码性能
        stopWatch.start("Base64流式编码");
        int base64Iterations = 100;
        for (int i = 0; i < base64Iterations; i++) {
            String result = VlmImageProcessor.imageToBase64Streaming(testImage);
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
        stopWatch.stop();
        
        double base64AvgTime = stopWatch.getLastTaskTimeMillis() / (double) base64Iterations;
        results.add(new BenchmarkResult(
            "VLM Base64流式编码",
            base64Iterations,
            stopWatch.getLastTaskTimeMillis(),
            base64AvgTime,
            "次/ms"
        ));
        
        // 性能断言
        assertTrue(normalAvgTime < 200, "普通图像处理应小于200ms/张，实际: " + normalAvgTime + "ms");
        assertTrue(largeAvgTime < 500, "大图像处理应小于500ms/张，实际: " + largeAvgTime + "ms");
        assertTrue(base64AvgTime < 50, "Base64编码应小于50ms/次，实际: " + base64AvgTime + "ms");
        
        System.out.println("VLM图像处理性能:");
        System.out.println("  - 普通图像: " + normalAvgTime + "ms/张");
        System.out.println("  - 大图像: " + largeAvgTime + "ms/张");
        System.out.println("  - Base64编码: " + base64AvgTime + "ms/次");
    }

    @Test
    @Order(3)
    @DisplayName("异常处理机制性能基准测试")
    void benchmarkExceptionHandlingPerformance() {
        System.out.println("开始异常处理机制性能基准测试...");
        
        StopWatch stopWatch = new StopWatch("异常处理性能");
        
        // 基准1：成功操作性能
        stopWatch.start("成功操作");
        int successIterations = 1000;
        for (int i = 0; i < successIterations; i++) {
            try {
                String result = PerformanceOptimizedExceptionHandler.executeWithRetry(
                    () -> "成功操作 " + i,
                    "success-benchmark-" + i
                );
                assertEquals("成功操作 " + i, result);
            } catch (Exception e) {
                fail("成功操作不应该抛出异常");
            }
        }
        stopWatch.stop();
        
        double successAvgTime = stopWatch.getLastTaskTimeMillis() / (double) successIterations;
        results.add(new BenchmarkResult(
            "异常处理器成功操作",
            successIterations,
            stopWatch.getLastTaskTimeMillis(),
            successAvgTime,
            "次/ms"
        ));
        
        // 基准2：安全执行性能
        stopWatch.start("安全执行");
        int safeIterations = 500;
        for (int i = 0; i < safeIterations; i++) {
            String result = PerformanceOptimizedExceptionHandler.executeSafely(
                () -> "安全操作 " + i,
                "safe-benchmark-" + i,
                "默认值"
            );
            assertEquals("安全操作 " + i, result);
        }
        stopWatch.stop();
        
        double safeAvgTime = stopWatch.getLastTaskTimeMillis() / (double) safeIterations;
        results.add(new BenchmarkResult(
            "异常处理器安全执行",
            safeIterations,
            stopWatch.getLastTaskTimeMillis(),
            safeAvgTime,
            "次/ms"
        ));
        
        // 基准3：统计信息获取性能
        stopWatch.start("统计信息获取");
        int statsIterations = 1000;
        for (int i = 0; i < statsIterations; i++) {
            PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
                PerformanceOptimizedExceptionHandler.getStatistics();
            assertNotNull(stats);
        }
        stopWatch.stop();
        
        double statsAvgTime = stopWatch.getLastTaskTimeMillis() / (double) statsIterations;
        results.add(new BenchmarkResult(
            "异常统计信息获取",
            statsIterations,
            stopWatch.getLastTaskTimeMillis(),
            statsAvgTime,
            "次/ms"
        ));
        
        // 性能断言
        assertTrue(successAvgTime < 1.0, "成功操作应小于1ms/次，实际: " + successAvgTime + "ms");
        assertTrue(safeAvgTime < 1.0, "安全执行应小于1ms/次，实际: " + safeAvgTime + "ms");
        assertTrue(statsAvgTime < 0.5, "统计信息获取应小于0.5ms/次，实际: " + statsAvgTime + "ms");
        
        System.out.println("异常处理性能:");
        System.out.println("  - 成功操作: " + successAvgTime + "ms/次");
        System.out.println("  - 安全执行: " + safeAvgTime + "ms/次");
        System.out.println("  - 统计获取: " + statsAvgTime + "ms/次");
    }

    @Test
    @Order(4)
    @DisplayName("并发性能基准测试")
    @Timeout(30)
    void benchmarkConcurrentPerformance() throws InterruptedException {
        System.out.println("开始并发性能基准测试...");
        
        int threadCount = 10;
        int operationsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        // 并发测试：Excel缓存统计
        StopWatch stopWatch = new StopWatch("并发性能测试");
        stopWatch.start("Excel缓存并发访问");
        
        CompletableFuture<Void>[] excelFutures = new CompletableFuture[threadCount];
        for (int i = 0; i < threadCount; i++) {
            excelFutures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
                    assertNotNull(stats);
                }
            }, executor);
        }
        
        CompletableFuture.allOf(excelFutures).join();
        stopWatch.stop();
        
        double excelConcurrentAvgTime = stopWatch.getLastTaskTimeMillis() / (double) (threadCount * operationsPerThread);
        results.add(new BenchmarkResult(
            "Excel缓存并发访问",
            threadCount * operationsPerThread,
            stopWatch.getLastTaskTimeMillis(),
            excelConcurrentAvgTime,
            "次/ms"
        ));
        
        // 并发测试：异常处理
        stopWatch.start("异常处理并发执行");
        
        CompletableFuture<Void>[] exceptionFutures = new CompletableFuture[threadCount];
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            exceptionFutures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    String result = PerformanceOptimizedExceptionHandler.executeSafely(
                        () -> "并发测试 " + threadId + "-" + j,
                        "concurrent-test-" + threadId + "-" + j,
                        "默认值"
                    );
                    assertNotNull(result);
                }
            }, executor);
        }
        
        CompletableFuture.allOf(exceptionFutures).join();
        stopWatch.stop();
        
        double exceptionConcurrentAvgTime = stopWatch.getLastTaskTimeMillis() / (double) (threadCount * operationsPerThread);
        results.add(new BenchmarkResult(
            "异常处理并发执行",
            threadCount * operationsPerThread,
            stopWatch.getLastTaskTimeMillis(),
            exceptionConcurrentAvgTime,
            "次/ms"
        ));
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS));
        
        // 性能断言
        assertTrue(excelConcurrentAvgTime < 2.0, "Excel缓存并发访问应小于2ms/次，实际: " + excelConcurrentAvgTime + "ms");
        assertTrue(exceptionConcurrentAvgTime < 2.0, "异常处理并发执行应小于2ms/次，实际: " + exceptionConcurrentAvgTime + "ms");
        
        System.out.println("并发性能:");
        System.out.println("  - Excel缓存并发: " + excelConcurrentAvgTime + "ms/次");
        System.out.println("  - 异常处理并发: " + exceptionConcurrentAvgTime + "ms/次");
    }

    @Test
    @Order(5)
    @DisplayName("内存使用基准测试")
    void benchmarkMemoryUsage() throws IOException {
        System.out.println("开始内存使用基准测试...");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收获取基线
        runtime.gc();
        Thread.yield();
        long baselineMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 测试1：Excel缓存内存使用
        for (int i = 0; i < 1000; i++) {
            ExcelToMarkdownConverter.getCacheStatistics();
        }
        
        runtime.gc();
        Thread.yield();
        long afterExcelMemory = runtime.totalMemory() - runtime.freeMemory();
        long excelMemoryGrowth = afterExcelMemory - baselineMemory;
        
        // 测试2：VLM图像处理内存使用
        for (int i = 0; i < 50; i++) {
            VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(testImage);
            assertNotNull(result);
            // 不保持引用，让GC可以回收
        }
        
        runtime.gc();
        Thread.yield();
        long afterVlmMemory = runtime.totalMemory() - runtime.freeMemory();
        long vlmMemoryGrowth = afterVlmMemory - afterExcelMemory;
        
        // 测试3：异常处理内存使用
        for (int i = 0; i < 1000; i++) {
            PerformanceOptimizedExceptionHandler.executeSafely(
                () -> "内存测试 " + i,
                "memory-test-" + i,
                "默认值"
            );
        }
        
        runtime.gc();
        Thread.yield();
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long exceptionMemoryGrowth = finalMemory - afterVlmMemory;
        
        results.add(new BenchmarkResult(
            "Excel缓存内存增长",
            1000,
            excelMemoryGrowth / 1024, // KB
            (double) excelMemoryGrowth / 1000, // bytes per operation
            "KB"
        ));
        
        results.add(new BenchmarkResult(
            "VLM图像处理内存增长",
            50,
            vlmMemoryGrowth / 1024, // KB
            (double) vlmMemoryGrowth / 50, // bytes per operation
            "KB"
        ));
        
        results.add(new BenchmarkResult(
            "异常处理内存增长",
            1000,
            exceptionMemoryGrowth / 1024, // KB
            (double) exceptionMemoryGrowth / 1000, // bytes per operation
            "KB"
        ));
        
        // 内存使用断言
        assertTrue(excelMemoryGrowth < 10 * 1024 * 1024, // 10MB
                  "Excel缓存内存增长应小于10MB，实际: " + (excelMemoryGrowth / 1024 / 1024) + "MB");
        assertTrue(vlmMemoryGrowth < 50 * 1024 * 1024, // 50MB
                  "VLM图像处理内存增长应小于50MB，实际: " + (vlmMemoryGrowth / 1024 / 1024) + "MB");
        assertTrue(exceptionMemoryGrowth < 5 * 1024 * 1024, // 5MB
                  "异常处理内存增长应小于5MB，实际: " + (exceptionMemoryGrowth / 1024 / 1024) + "MB");
        
        System.out.println("内存使用:");
        System.out.println("  - Excel缓存: " + (excelMemoryGrowth / 1024) + "KB");
        System.out.println("  - VLM图像处理: " + (vlmMemoryGrowth / 1024) + "KB");
        System.out.println("  - 异常处理: " + (exceptionMemoryGrowth / 1024) + "KB");
    }

    /**
     * 基准测试结果数据类
     */
    private static class BenchmarkResult {
        private final String testName;
        private final int iterations;
        private final double totalTime;
        private final double averageTime;
        private final String unit;

        public BenchmarkResult(String testName, int iterations, double totalTime, double averageTime, String unit) {
            this.testName = testName;
            this.iterations = iterations;
            this.totalTime = totalTime;
            this.averageTime = averageTime;
            this.unit = unit;
        }

        @Override
        public String toString() {
            return String.format("%-25s | %6d次 | %8.2f ms | %8.4f %s",
                    testName, iterations, totalTime, averageTime, unit);
        }
    }
}