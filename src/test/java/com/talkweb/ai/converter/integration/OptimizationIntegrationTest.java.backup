package com.talkweb.ai.converter.integration;

import com.talkweb.ai.converter.core.impl.ExcelToMarkdownConverter;
import com.talkweb.ai.converter.core.ConversionResult;
import com.talkweb.ai.converter.core.converter.ConversionContext;
import com.talkweb.ai.converter.core.converter.ConversionOptions;
import com.talkweb.ai.converter.vlm.util.VlmImageProcessor;
import com.talkweb.ai.converter.core.exception.PerformanceOptimizedExceptionHandler;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 优化功能集成测试
 * 
 * 测试各种优化功能之间的协同工作和端到端场景
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.ai.openai.enabled=false",
    "ai.enabled=false",
    "vlm.enabled=false"
})
@DisplayName("优化功能集成测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class OptimizationIntegrationTest {

    @TempDir
    Path tempDir;

    private ExcelToMarkdownConverter excelConverter;
    private File testImageFile;

    @BeforeEach
    void setUp() throws IOException {
        excelConverter = new ExcelToMarkdownConverter();
        testImageFile = createTestImage();
        
        // 清理状态
        ExcelToMarkdownConverter.clearCache();
        PerformanceOptimizedExceptionHandler.resetStatistics();
    }

    @AfterEach
    void tearDown() {
        // 清理状态
        ExcelToMarkdownConverter.clearCache();
        PerformanceOptimizedExceptionHandler.resetStatistics();
    }

    private File createTestImage() throws IOException {
        BufferedImage image = new BufferedImage(800, 600, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.BLUE);
        g2d.fillRect(0, 0, 800, 600);
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("Arial", Font.BOLD, 20));
        g2d.drawString("Integration Test Image", 50, 50);
        g2d.dispose();
        
        File imageFile = tempDir.resolve("integration-test.png").toFile();
        ImageIO.write(image, "png", imageFile);
        
        return imageFile;
    }

    @Test
    @Order(1)
    @DisplayName("Excel缓存与异常处理集成测试")
    void testExcelCacheWithExceptionHandling() {
        // 使用异常处理器执行Excel缓存操作
        Map<String, Object> stats1 = PerformanceOptimizedExceptionHandler.executeSafely(
            () -> ExcelToMarkdownConverter.getCacheStatistics(),
            "excel-cache-stats-1",
            null
        );
        
        assertNotNull(stats1);
        assertEquals(0, stats1.get("cellContentCacheSize"));
        
        // 执行多次操作
        for (int i = 0; i < 10; i++) {
            Map<String, Object> stats = PerformanceOptimizedExceptionHandler.executeSafely(
                () -> ExcelToMarkdownConverter.getCacheStatistics(),
                "excel-cache-stats-" + (i + 2),
                null
            );
            assertNotNull(stats);
        }
        
        // 验证异常处理统计
        PerformanceOptimizedExceptionHandler.ExceptionStatistics exceptionStats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertEquals(0, exceptionStats.getTotalExceptions());
        assertEquals(100.0, exceptionStats.getSuccessRate());
        
        // 清空缓存并验证
        PerformanceOptimizedExceptionHandler.executeSafely(
            () -> {
                ExcelToMarkdownConverter.clearCache();
                return null;
            },
            "excel-cache-clear",
            null
        );
        
        Map<String, Object> finalStats = ExcelToMarkdownConverter.getCacheStatistics();
        assertEquals(0, finalStats.get("cellContentCacheSize"));
    }

    @Test
    @Order(2)
    @DisplayName("VLM图像处理与异常处理集成测试")
    void testVlmImageProcessingWithExceptionHandling() {
        // 使用异常处理器执行VLM图像处理
        VlmImageProcessor.ProcessedImage result1 = PerformanceOptimizedExceptionHandler.executeSafely(
            () -> VlmImageProcessor.processImage(testImageFile),
            "vlm-image-processing-1",
            null
        );
        
        assertNotNull(result1);
        assertNotNull(result1.getBase64Data());
        assertEquals("image/png", result1.getMimeType());
        assertFalse(result1.isResized()); // 800x600不需要调整大小
        
        // 使用重试机制执行Base64编码
        String base64Result = null;
        try {
            base64Result = PerformanceOptimizedExceptionHandler.executeWithRetry(
                () -> VlmImageProcessor.imageToBase64Streaming(testImageFile),
                "vlm-base64-encoding"
            );
        } catch (Exception e) {
            fail("Base64编码不应该失败: " + e.getMessage());
        }
        
        assertNotNull(base64Result);
        assertFalse(base64Result.isEmpty());
        assertEquals(result1.getBase64Data(), base64Result);
        
        // 验证异常处理统计
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertEquals(0, stats.getTotalExceptions());
        assertEquals(100.0, stats.getSuccessRate());
    }

    @Test
    @Order(3)
    @DisplayName("多组件并发集成测试")
    @Timeout(15)
    void testMultiComponentConcurrentIntegration() throws InterruptedException {
        int threadCount = 8;
        int operationsPerThread = 25;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    try {
                        // 混合执行不同的优化功能
                        switch (j % 3) {
                            case 0:
                                // Excel缓存操作
                                Map<String, Object> cacheStats = PerformanceOptimizedExceptionHandler.executeSafely(
                                    () -> ExcelToMarkdownConverter.getCacheStatistics(),
                                    "concurrent-excel-" + threadId + "-" + j,
                                    null
                                );
                                assertNotNull(cacheStats);
                                break;
                                
                            case 1:
                                // VLM图像处理
                                try {
                                    VlmImageProcessor.ProcessedImage imageResult = PerformanceOptimizedExceptionHandler.executeSafely(
                                        () -> {
                                            try {
                                                return VlmImageProcessor.processImage(testImageFile);
                                            } catch (IOException e) {
                                                throw new RuntimeException(e);
                                            }
                                        },
                                        "concurrent-vlm-" + threadId + "-" + j,
                                        null
                                    );
                                    assertNotNull(imageResult);
                                } catch (Exception e) {
                                    // 忽略VLM处理异常，因为可能依赖外部资源
                                }
                                break;
                                
                            case 2:
                                // 异常处理统计
                                PerformanceOptimizedExceptionHandler.ExceptionStatistics exceptionStats = 
                                    PerformanceOptimizedExceptionHandler.getStatistics();
                                assertNotNull(exceptionStats);
                                break;
                        }
                    } catch (Exception e) {
                        fail("并发集成测试失败: " + e.getMessage());
                    }
                }
            }, executor);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS));
        
        // 验证所有组件仍然正常工作
        Map<String, Object> finalCacheStats = ExcelToMarkdownConverter.getCacheStatistics();
        assertNotNull(finalCacheStats);
        
        try {
            VlmImageProcessor.ProcessedImage finalImageResult = VlmImageProcessor.processImage(testImageFile);
            assertNotNull(finalImageResult);
        } catch (IOException e) {
            // 忽略VLM处理异常
        }
        
        PerformanceOptimizedExceptionHandler.ExceptionStatistics finalExceptionStats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        assertNotNull(finalExceptionStats);
        
        // 验证无异常发生
        assertEquals(0, finalExceptionStats.getTotalExceptions());
        assertEquals(100.0, finalExceptionStats.getSuccessRate());
    }

    @Test
    @Order(4)
    @DisplayName("端到端性能优化集成测试")
    void testEndToEndPerformanceOptimization() throws IOException {
        long startTime = System.currentTimeMillis();
        
        // 模拟完整的文档处理流程
        
        // 1. 处理多个图像（VLM优化）
        for (int i = 0; i < 5; i++) {
            final int imageIndex = i;
            try {
                VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(testImageFile);
                assertNotNull(result);
                
                // 验证压缩效果
                assertTrue(result.getCompressionRatio() > 0);
                assertEquals("image/png", result.getMimeType());
            } catch (IOException e) {
                fail("VLM图像处理失败: " + e.getMessage());
            }
        }
        
        // 2. 执行多次Excel缓存操作（缓存优化）
        for (int i = 0; i < 20; i++) {
            Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
            assertNotNull(stats);
            assertEquals("0.00%", stats.get("cacheUtilization")); // 当前无实际缓存内容
        }
        
        // 3. 模拟一些异常场景（异常处理优化）
        for (int i = 0; i < 10; i++) {
            final int operationIndex = i;
            // 成功操作
            String result = PerformanceOptimizedExceptionHandler.executeSafely(
                () -> "成功操作 " + operationIndex,
                "end-to-end-success-" + operationIndex,
                "默认值"
            );
            assertEquals("成功操作 " + operationIndex, result);
            
            // 模拟失败操作
            String failureResult = PerformanceOptimizedExceptionHandler.executeSafely(
                () -> {
                    throw new RuntimeException("模拟失败 " + operationIndex);
                },
                "end-to-end-failure-" + operationIndex,
                "默认值"
            );
            assertEquals("默认值", failureResult);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        // 验证整体性能
        assertTrue(totalTime < 5000, "端到端测试应在5秒内完成，实际: " + totalTime + "ms");
        
        // 验证各组件状态
        Map<String, Object> finalCacheStats = ExcelToMarkdownConverter.getCacheStatistics();
        assertEquals(0, finalCacheStats.get("cellContentCacheSize"));
        assertEquals(30L, finalCacheStats.get("cacheTtlMinutes"));
        
        PerformanceOptimizedExceptionHandler.ExceptionStatistics exceptionStats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        assertEquals(10, exceptionStats.getTotalExceptions()); // 10个模拟失败
        assertEquals(0, exceptionStats.getRecoveredExceptions()); // 安全执行不计入恢复
        assertEquals(0.0, exceptionStats.getSuccessRate()); // 0/10 = 0%
        
        System.out.println("端到端测试完成，总耗时: " + totalTime + "ms");
    }

    @Test
    @Order(5)
    @DisplayName("错误恢复集成测试")
    void testErrorRecoveryIntegration() {
        // 模拟文件处理过程中的错误恢复
        
        // 1. 模拟图像处理错误恢复
        File nonExistentFile = tempDir.resolve("non-existent.png").toFile();
        
        VlmImageProcessor.ProcessedImage result = PerformanceOptimizedExceptionHandler.executeSafely(
            () -> VlmImageProcessor.processImage(nonExistentFile),
            "error-recovery-vlm",
            null
        );
        
        // 应该返回默认值（null）而不是抛出异常
        assertNull(result);
        
        // 2. 模拟Excel处理错误恢复（通过模拟操作）
        Map<String, Object> cacheStats = PerformanceOptimizedExceptionHandler.executeSafely(
            () -> {
                // 模拟一个可能出错的缓存操作
                Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
                if (stats.get("cellContentCacheSize").equals(Integer.MAX_VALUE)) {
                    throw new RuntimeException("模拟缓存错误");
                }
                return stats;
            },
            "error-recovery-excel",
            null
        );
        
        assertNotNull(cacheStats); // 不会出错，因为条件不满足
        
        // 3. 验证错误统计
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertEquals(1, stats.getTotalExceptions()); // VLM处理错误
        assertEquals(0, stats.getRecoveredExceptions()); // 安全执行不计入恢复
        assertTrue(stats.getExceptionCounts().containsKey("FileNotFoundException"));
    }

    @Test
    @Order(6)
    @DisplayName("内存和资源管理集成测试")
    void testMemoryAndResourceManagement() throws IOException {
        Runtime runtime = Runtime.getRuntime();
        
        // 获取初始内存状态
        runtime.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 执行大量操作
        for (int i = 0; i < 100; i++) {
            final int operationIndex = i;
            try {
                // VLM图像处理
                VlmImageProcessor.ProcessedImage imageResult = VlmImageProcessor.processImage(testImageFile);
                assertNotNull(imageResult);
            } catch (IOException e) {
                // 忽略IO异常，继续测试
            }
            
            // Excel缓存操作
            Map<String, Object> cacheStats = ExcelToMarkdownConverter.getCacheStatistics();
            assertNotNull(cacheStats);
            
            // 异常处理操作
            String exceptionResult = PerformanceOptimizedExceptionHandler.executeSafely(
                () -> "内存测试 " + operationIndex,
                "memory-test-" + operationIndex,
                "默认值"
            );
            assertEquals("内存测试 " + operationIndex, exceptionResult);
            
            // 不保持引用，让GC可以回收
        }
        
        // 强制垃圾回收
        runtime.gc();
        Thread.yield();
        runtime.gc();
        
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryGrowth = finalMemory - initialMemory;
        
        // 验证内存增长在合理范围内
        assertTrue(memoryGrowth < 50 * 1024 * 1024, // 50MB
                  "内存增长应小于50MB，实际: " + (memoryGrowth / 1024 / 1024) + "MB");
        
        // 清理所有缓存和状态
        ExcelToMarkdownConverter.clearCache();
        PerformanceOptimizedExceptionHandler.resetStatistics();
        
        // 再次检查内存
        runtime.gc();
        long cleanupMemory = runtime.totalMemory() - runtime.freeMemory();
        long cleanupGrowth = cleanupMemory - initialMemory;
        
        // 清理后内存增长应该更小
        assertTrue(cleanupGrowth <= memoryGrowth, 
                  "清理后内存应该不超过清理前: " + (cleanupGrowth / 1024 / 1024) + "MB vs " + (memoryGrowth / 1024 / 1024) + "MB");
        
        System.out.println("内存管理测试:");
        System.out.println("  - 操作后内存增长: " + (memoryGrowth / 1024 / 1024) + "MB");
        System.out.println("  - 清理后内存增长: " + (cleanupGrowth / 1024 / 1024) + "MB");
    }

    @Test
    @Order(7)
    @DisplayName("配置和监控集成测试")
    void testConfigurationAndMonitoringIntegration() {
        // 验证各组件的配置参数
        
        // 1. Excel缓存配置验证
        Map<String, Object> cacheStats = ExcelToMarkdownConverter.getCacheStatistics();
        assertEquals(1000, cacheStats.get("cellContentCacheLimit"));
        assertEquals(30L, cacheStats.get("cacheTtlMinutes"));
        assertTrue(cacheStats.containsKey("cacheUtilization"));
        assertTrue(cacheStats.containsKey("lastCleanup"));
        
        // 2. 异常处理监控验证
        PerformanceOptimizedExceptionHandler.ExceptionStatistics exceptionStats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        assertNotNull(exceptionStats);
        assertEquals(0, exceptionStats.getTotalExceptions());
        assertEquals(100.0, exceptionStats.getSuccessRate());
        
        // 3. VLM处理统计验证
        try {
            VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(testImageFile);
            VlmImageProcessor.ImageProcessingStats imageStats = VlmImageProcessor.createStats(result);
            
            assertNotNull(imageStats);
            assertEquals("800x600", imageStats.getOriginalDimensions());
            assertEquals("800x600", imageStats.getProcessedDimensions());
            assertEquals("image/png", imageStats.getMimeType());
            assertFalse(imageStats.isResized());
        } catch (IOException e) {
            fail("VLM图像处理失败: " + e.getMessage());
        }
        
        // 4. 验证监控数据的一致性
        for (int i = 0; i < 5; i++) {
            Map<String, Object> stats1 = ExcelToMarkdownConverter.getCacheStatistics();
            Map<String, Object> stats2 = ExcelToMarkdownConverter.getCacheStatistics();
            
            // 在没有缓存操作的情况下，统计应该一致
            assertEquals(stats1.get("cellContentCacheSize"), stats2.get("cellContentCacheSize"));
            assertEquals(stats1.get("cellContentCacheLimit"), stats2.get("cellContentCacheLimit"));
        }
    }
}