package com.talkweb.ai.converter.core.impl;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Timeout;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Excel转换器缓存优化功能测试
 * 
 * 测试TTL缓存、自动清理、并发安全等优化功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@DisplayName("Excel缓存优化测试")
class ExcelCacheOptimizationTest {

    private ExcelToMarkdownConverter converter;

    @BeforeEach
    void setUp() {
        converter = new ExcelToMarkdownConverter();
        // 清空缓存以确保测试独立性
        ExcelToMarkdownConverter.clearCache();
    }

    @AfterEach
    void tearDown() {
        // 清理缓存
        ExcelToMarkdownConverter.clearCache();
    }

    @Test
    @DisplayName("测试缓存统计信息获取")
    void testCacheStatistics() {
        // 获取初始统计信息
        Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
        
        // 验证统计信息结构
        assertNotNull(stats);
        assertTrue(stats.containsKey("cellContentCacheSize"));
        assertTrue(stats.containsKey("cellContentCacheLimit"));
        assertTrue(stats.containsKey("cacheUtilization"));
        assertTrue(stats.containsKey("cacheTtlMinutes"));
        assertTrue(stats.containsKey("lastCleanup"));
        assertTrue(stats.containsKey("expiredEntries"));
        
        // 验证初始值
        assertEquals(0, stats.get("cellContentCacheSize"));
        assertEquals(1000, stats.get("cellContentCacheLimit"));
        assertEquals("0.00%", stats.get("cacheUtilization"));
        assertEquals(30L, stats.get("cacheTtlMinutes"));
        assertEquals(0L, stats.get("expiredEntries"));
    }

    @Test
    @DisplayName("测试缓存清空功能")
    void testCacheClear() {
        // 模拟缓存中有数据的情况（通过反射或其他方式）
        Map<String, Object> initialStats = ExcelToMarkdownConverter.getCacheStatistics();
        
        // 清空缓存
        ExcelToMarkdownConverter.clearCache();
        
        // 验证缓存已清空
        Map<String, Object> clearedStats = ExcelToMarkdownConverter.getCacheStatistics();
        assertEquals(0, clearedStats.get("cellContentCacheSize"));
    }

    @Test
    @DisplayName("测试缓存TTL过期机制")
    void testCacheTTLExpiration() {
        try (MockedStatic<LocalDateTime> mockedTime = Mockito.mockStatic(LocalDateTime.class)) {
            // 确保fixedTime在整个测试作用域内有效
            final LocalDateTime fixedTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
            mockedTime.when(LocalDateTime::now).thenReturn(fixedTime);
            
            // 获取初始统计信息
            Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
            assertNotNull(stats);
            
            // 模拟时间推进31分钟（超过TTL）
            final LocalDateTime expiredTime = fixedTime.plusMinutes(31);
            mockedTime.when(LocalDateTime::now).thenReturn(expiredTime);
            
            // 再次获取统计信息
            Map<String, Object> statsAfterExpiration = ExcelToMarkdownConverter.getCacheStatistics();
            assertNotNull(statsAfterExpiration);
        }
    }

    @Test
    @DisplayName("测试缓存大小限制")
    void testCacheSizeLimit() {
        Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
        
        // 验证缓存大小限制配置
        assertEquals(1000, stats.get("cellContentCacheLimit"));
        
        // 缓存大小应该在限制范围内
        int currentSize = (Integer) stats.get("cellContentCacheSize");
        int limit = (Integer) stats.get("cellContentCacheLimit");
        assertTrue(currentSize <= limit);
    }

    @Test
    @DisplayName("测试并发缓存访问安全性")
    @Timeout(10)
    void testConcurrentCacheAccess() throws InterruptedException {
        int threadCount = 10;
        int operationsPerThread = 50;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        // 并发执行缓存操作
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    // 并发获取缓存统计
                    Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
                    assertNotNull(stats);
                    
                    // 并发清空缓存
                    if (j % 10 == 0) {
                        ExcelToMarkdownConverter.clearCache();
                    }
                }
            }, executor);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        // 验证没有发生并发异常
        Map<String, Object> finalStats = ExcelToMarkdownConverter.getCacheStatistics();
        assertNotNull(finalStats);
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
    }

    @Test
    @DisplayName("测试缓存利用率计算")
    void testCacheUtilizationCalculation() {
        Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
        
        String utilization = (String) stats.get("cacheUtilization");
        assertNotNull(utilization);
        assertTrue(utilization.endsWith("%"));
        
        // 解析利用率
        double utilizationValue = Double.parseDouble(utilization.replace("%", ""));
        assertTrue(utilizationValue >= 0.0 && utilizationValue <= 100.0);
    }

    @Test
    @DisplayName("测试缓存性能指标")
    void testCachePerformanceMetrics() {
        long startTime = System.currentTimeMillis();
        
        // 执行多次缓存统计获取
        int iterations = 1000;
        for (int i = 0; i < iterations; i++) {
            Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
            assertNotNull(stats);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能：1000次调用应该在合理时间内完成（比如1秒）
        assertTrue(duration < 1000, "缓存统计获取性能不达标: " + duration + "ms");
        
        // 平均每次调用时间应该很短
        double avgTimePerCall = (double) duration / iterations;
        assertTrue(avgTimePerCall < 1.0, "平均每次缓存统计调用时间过长: " + avgTimePerCall + "ms");
    }

    @RepeatedTest(5)
    @DisplayName("测试缓存一致性（重复测试）")
    void testCacheConsistency() {
        // 获取两次统计信息
        Map<String, Object> stats1 = ExcelToMarkdownConverter.getCacheStatistics();
        Map<String, Object> stats2 = ExcelToMarkdownConverter.getCacheStatistics();
        
        // 在没有缓存操作的情况下，两次统计应该一致
        assertEquals(stats1.get("cellContentCacheSize"), stats2.get("cellContentCacheSize"));
        assertEquals(stats1.get("cellContentCacheLimit"), stats2.get("cellContentCacheLimit"));
        assertEquals(stats1.get("cacheTtlMinutes"), stats2.get("cacheTtlMinutes"));
    }

    @Test
    @DisplayName("测试缓存统计字段类型")
    void testCacheStatisticsFieldTypes() {
        Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
        
        // 验证字段类型
        assertTrue(stats.get("cellContentCacheSize") instanceof Integer);
        assertTrue(stats.get("cellContentCacheLimit") instanceof Integer);
        assertTrue(stats.get("cacheUtilization") instanceof String);
        assertTrue(stats.get("cacheTtlMinutes") instanceof Long);
        assertTrue(stats.get("lastCleanup") instanceof String);
        assertTrue(stats.get("expiredEntries") instanceof Long);
    }

    @Test
    @DisplayName("测试内存清理效果")
    void testMemoryCleanupEffect() {
        // 记录初始内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 强制垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 执行缓存操作
        for (int i = 0; i < 100; i++) {
            ExcelToMarkdownConverter.getCacheStatistics();
        }
        
        // 清空缓存
        ExcelToMarkdownConverter.clearCache();
        runtime.gc(); // 再次强制垃圾回收
        
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 验证内存使用没有明显增长（考虑到JVM的内存管理，这个测试可能不够精确）
        long memoryGrowth = finalMemory - initialMemory;
        
        // 内存增长应该在合理范围内（比如1MB）
        assertTrue(memoryGrowth < 1024 * 1024, 
                  "内存增长过多: " + memoryGrowth + " bytes");
    }

    @Test
    @DisplayName("测试缓存配置常量")
    void testCacheConfigurationConstants() {
        Map<String, Object> stats = ExcelToMarkdownConverter.getCacheStatistics();
        
        // 验证缓存限制配置
        assertEquals(1000, stats.get("cellContentCacheLimit"));
        assertEquals(30L, stats.get("cacheTtlMinutes"));
        
        // 验证配置的合理性
        Integer limit = (Integer) stats.get("cellContentCacheLimit");
        Long ttl = (Long) stats.get("cacheTtlMinutes");
        
        assertTrue(limit > 0, "缓存限制应该大于0");
        assertTrue(ttl > 0, "TTL应该大于0");
        assertTrue(limit <= 10000, "缓存限制不应该过大");
        assertTrue(ttl <= 1440, "TTL不应该超过24小时");
    }
}