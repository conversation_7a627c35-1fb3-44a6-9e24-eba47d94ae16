package com.talkweb.ai.converter.core.exception;

import com.talkweb.ai.converter.core.ConversionException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Timeout;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 性能优化异常处理器测试
 * 
 * 测试重试机制、统计功能、并发安全等特性
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@DisplayName("性能优化异常处理器测试")
class PerformanceOptimizedExceptionHandlerTest {

    @BeforeEach
    void setUp() {
        // 重置统计信息
        PerformanceOptimizedExceptionHandler.resetStatistics();
    }

    @AfterEach
    void tearDown() {
        // 清理统计信息
        PerformanceOptimizedExceptionHandler.resetStatistics();
    }

    @Test
    @DisplayName("测试成功执行无重试")
    void testSuccessfulExecutionWithoutRetry() throws ConversionException {
        String result = PerformanceOptimizedExceptionHandler.executeWithRetry(
            () -> "success",
            "test-operation"
        );
        
        assertEquals("success", result);
        
        // 验证统计信息
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertEquals(0, stats.getTotalExceptions());
        assertEquals(0, stats.getRecoveredExceptions());
        assertEquals(100.0, stats.getSuccessRate());
    }

    @Test
    @DisplayName("测试重试机制成功恢复")
    void testRetryMechanismWithRecovery() throws ConversionException {
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        String result = PerformanceOptimizedExceptionHandler.executeWithRetry(
            () -> {
                int attempt = attemptCount.incrementAndGet();
                if (attempt < 3) {
                    throw new RuntimeException("模拟失败 #" + attempt);
                }
                return "恢复成功";
            },
            "retry-test"
        );
        
        assertEquals("恢复成功", result);
        assertEquals(3, attemptCount.get());
        
        // 验证统计信息
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertEquals(2, stats.getTotalExceptions()); // 前两次失败
        assertEquals(1, stats.getRecoveredExceptions()); // 最终恢复
        assertEquals(50.0, stats.getSuccessRate()); // 1/2 = 50%
    }

    @Test
    @DisplayName("测试重试耗尽后失败")
    void testRetryExhaustionFailure() {
        AtomicInteger attemptCount = new AtomicInteger(0);
        
        ConversionException exception = assertThrows(ConversionException.class, () -> {
            PerformanceOptimizedExceptionHandler.executeWithRetry(
                () -> {
                    attemptCount.incrementAndGet();
                    throw new RuntimeException("持续失败");
                },
                "exhaustion-test"
            );
        });
        
        assertTrue(exception.getMessage().contains("exhaustion-test"));
        assertTrue(exception.getMessage().contains("4 次尝试后仍然失败")); // 1 + 3 重试
        assertEquals(4, attemptCount.get());
        
        // 验证统计信息
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertEquals(4, stats.getTotalExceptions());
        assertEquals(0, stats.getRecoveredExceptions());
        assertEquals(0.0, stats.getSuccessRate());
    }

    @Test
    @DisplayName("测试自定义重试配置")
    void testCustomRetryConfiguration() throws ConversionException {
        AtomicInteger attemptCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        
        String result = PerformanceOptimizedExceptionHandler.executeWithRetry(
            () -> {
                int attempt = attemptCount.incrementAndGet();
                if (attempt < 2) {
                    throw new RuntimeException("自定义重试测试");
                }
                return "成功";
            },
            "custom-retry-test",
            1, // 只重试1次
            Duration.ofMillis(100) // 100ms延迟
        );
        
        long endTime = System.currentTimeMillis();
        
        assertEquals("成功", result);
        assertEquals(2, attemptCount.get());
        
        // 验证重试延迟
        assertTrue(endTime - startTime >= 100, "应该有重试延迟");
        assertTrue(endTime - startTime < 1000, "延迟不应该过长");
    }

    @Test
    @DisplayName("测试指数退避延迟")
    @Timeout(5)
    void testExponentialBackoffDelay() {
        AtomicInteger attemptCount = new AtomicInteger(0);
        long startTime = System.currentTimeMillis();
        
        assertThrows(ConversionException.class, () -> {
            PerformanceOptimizedExceptionHandler.executeWithRetry(
                () -> {
                    attemptCount.incrementAndGet();
                    throw new RuntimeException("退避测试");
                },
                "backoff-test",
                3,
                Duration.ofMillis(100)
            );
        });
        
        long endTime = System.currentTimeMillis();
        
        assertEquals(4, attemptCount.get()); // 1 + 3 重试
        
        // 验证总延迟时间符合指数退避：100 + 200 + 400 = 700ms
        long totalTime = endTime - startTime;
        assertTrue(totalTime >= 700, "总时间应该包含指数退避延迟: " + totalTime + "ms");
        assertTrue(totalTime < 2000, "总时间不应该过长: " + totalTime + "ms");
    }

    @Test
    @DisplayName("测试安全执行功能")
    void testSafeExecution() {
        // 测试成功执行
        String successResult = PerformanceOptimizedExceptionHandler.executeSafely(
            () -> "安全成功",
            "safe-success-test",
            "默认值"
        );
        assertEquals("安全成功", successResult);
        
        // 测试异常时返回默认值
        String failureResult = PerformanceOptimizedExceptionHandler.executeSafely(
            () -> {
                throw new RuntimeException("安全失败测试");
            },
            "safe-failure-test",
            "默认值"
        );
        assertEquals("默认值", failureResult);
        
        // 验证统计信息
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertEquals(1, stats.getTotalExceptions());
    }

    @Test
    @DisplayName("测试异常统计功能")
    void testExceptionStatistics() throws ConversionException {
        // 执行一些操作来生成统计数据
        PerformanceOptimizedExceptionHandler.executeSafely(
            () -> { throw new IllegalArgumentException("参数错误"); },
            "stats-test-1",
            null
        );
        
        PerformanceOptimizedExceptionHandler.executeSafely(
            () -> { throw new RuntimeException("运行时错误"); },
            "stats-test-2", 
            null
        );
        
        // 成功的重试操作
        AtomicInteger attempt = new AtomicInteger(0);
        PerformanceOptimizedExceptionHandler.executeWithRetry(
            () -> {
                if (attempt.incrementAndGet() < 2) {
                    throw new RuntimeException("重试测试");
                }
                return "成功";
            },
            "stats-retry-test"
        );
        
        // 获取统计信息
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertNotNull(stats);
        assertEquals(3, stats.getTotalExceptions()); // 2个安全执行 + 1个重试
        assertEquals(1, stats.getRecoveredExceptions()); // 1个重试恢复
        assertTrue(stats.getSuccessRate() > 0);
        assertNotNull(stats.getLastResetTime());
        
        // 验证异常类型统计
        assertNotNull(stats.getExceptionCounts());
        assertTrue(stats.getExceptionCounts().containsKey("IllegalArgumentException"));
        assertTrue(stats.getExceptionCounts().containsKey("RuntimeException"));
        
        // 验证处理时间统计
        assertNotNull(stats.getProcessingTimes());
        
        // 验证toString方法
        String statsString = stats.toString();
        assertNotNull(statsString);
        assertTrue(statsString.contains("totalExceptions=3"));
        assertTrue(statsString.contains("recoveredExceptions=1"));
    }

    @Test
    @DisplayName("测试统计信息重置")
    void testStatisticsReset() {
        // 先生成一些异常
        PerformanceOptimizedExceptionHandler.executeSafely(
            () -> { throw new RuntimeException("重置前异常"); },
            "reset-test",
            null
        );
        
        PerformanceOptimizedExceptionHandler.ExceptionStatistics statsBefore = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        assertEquals(1, statsBefore.getTotalExceptions());
        
        // 重置统计
        PerformanceOptimizedExceptionHandler.resetStatistics();
        
        PerformanceOptimizedExceptionHandler.ExceptionStatistics statsAfter = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        assertEquals(0, statsAfter.getTotalExceptions());
        assertEquals(0, statsAfter.getRecoveredExceptions());
        assertEquals(100.0, statsAfter.getSuccessRate()); // 无异常时成功率为100%
    }

    @Test
    @DisplayName("测试并发异常处理")
    @Timeout(10)
    void testConcurrentExceptionHandling() throws InterruptedException {
        int threadCount = 10;
        int operationsPerThread = 20;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < operationsPerThread; j++) {
                    final int operationId = j;
                    try {
                        // 模拟随机成功/失败
                        if ((threadId + operationId) % 3 == 0) {
                            // 抛出异常
                            PerformanceOptimizedExceptionHandler.executeSafely(
                                () -> { throw new RuntimeException("并发测试异常 " + threadId + "-" + operationId); },
                                "concurrent-test-" + threadId + "-" + operationId,
                                "默认"
                            );
                        } else {
                            // 成功执行
                            PerformanceOptimizedExceptionHandler.executeSafely(
                                () -> "成功 " + threadId + "-" + operationId,
                                "concurrent-success-" + threadId + "-" + operationId,
                                "默认"
                            );
                        }
                    } catch (Exception e) {
                        fail("并发异常处理失败: " + e.getMessage());
                    }
                }
            }, executor);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        // 验证统计信息的一致性
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        assertNotNull(stats);
        
        // 预期异常数量：精确计算
        int expectedExceptions = 0;
        for (int i = 0; i < threadCount; i++) {
            for (int j = 0; j < operationsPerThread; j++) {
                if ((i + j) % 3 == 0) {
                    expectedExceptions++;
                }
            }
        }
        assertEquals(expectedExceptions, stats.getTotalExceptions());
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
    }

    @Test
    @DisplayName("测试中断处理")
    void testInterruptHandling() {
        Thread testThread = new Thread(() -> {
            try {
                PerformanceOptimizedExceptionHandler.executeWithRetry(
                    () -> {
                        Thread.currentThread().interrupt(); // 模拟中断
                        throw new RuntimeException("中断测试");
                    },
                    "interrupt-test"
                );
                fail("应该抛出ConversionException");
            } catch (ConversionException e) {
                assertTrue(e.getMessage().contains("操作被中断"));
                assertTrue(Thread.currentThread().isInterrupted());
            }
        });
        
        testThread.start();
        try {
            testThread.join(5000); // 等待最多5秒
            assertFalse(testThread.isAlive(), "测试线程应该已完成");
        } catch (InterruptedException e) {
            fail("测试被中断");
        }
    }

    @RepeatedTest(3)
    @DisplayName("测试性能一致性（重复测试）")
    void testPerformanceConsistency() throws ConversionException {
        long startTime = System.currentTimeMillis();
        
        // 执行多个操作
        for (int i = 0; i < 100; i++) {
            final int operationIndex = i;
            PerformanceOptimizedExceptionHandler.executeSafely(
                () -> "操作 " + operationIndex,
                "performance-test-" + operationIndex,
                "默认"
            );
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能一致性
        assertTrue(duration < 1000, "100个操作应该在1秒内完成: " + duration + "ms");
        
        // 验证统计功能性能
        long statsStartTime = System.currentTimeMillis();
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        long statsEndTime = System.currentTimeMillis();
        
        assertNotNull(stats);
        assertTrue(statsEndTime - statsStartTime < 100, "统计信息获取应该很快");
    }

    @Test
    @DisplayName("测试成功率计算准确性")
    void testSuccessRateCalculation() {
        // 清空统计
        PerformanceOptimizedExceptionHandler.resetStatistics();
        
        // 执行一系列操作：3次失败，2次恢复
        AtomicInteger count = new AtomicInteger(0);
        
        // 第一个操作：失败后恢复
        try {
            PerformanceOptimizedExceptionHandler.executeWithRetry(
                () -> {
                    if (count.incrementAndGet() < 2) {
                        throw new RuntimeException("测试");
                    }
                    return "成功1";
                },
                "success-rate-test-1"
            );
        } catch (ConversionException e) {
            // 忽略
        }
        
        // 第二个操作：完全失败
        count.set(0);
        try {
            PerformanceOptimizedExceptionHandler.executeWithRetry(
                () -> {
                    count.incrementAndGet();
                    throw new RuntimeException("持续失败");
                },
                "success-rate-test-2",
                1, // 只重试1次
                Duration.ofMillis(1)
            );
        } catch (ConversionException e) {
            // 忽略
        }
        
        PerformanceOptimizedExceptionHandler.ExceptionStatistics stats = 
            PerformanceOptimizedExceptionHandler.getStatistics();
        
        // 总异常：1 + 2 = 3
        // 恢复异常：1（第一个操作恢复）
        // 成功率：1/3 ≈ 33.33%
        assertEquals(3, stats.getTotalExceptions());
        assertEquals(1, stats.getRecoveredExceptions());
        assertEquals(33.33, stats.getSuccessRate(), 0.01);
    }
}