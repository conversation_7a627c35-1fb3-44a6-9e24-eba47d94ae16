package com.talkweb.ai.converter.vlm;

import com.talkweb.ai.converter.vlm.model.VlmConversionRequest;
import com.talkweb.ai.converter.vlm.service.impl.VisionLanguageModelService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Base64;

/**
 * Base64 VLM 功能测试
 * 
 * 验证使用base64编码图片进行VLM识别的功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@SpringBootTest
public class Base64VlmTest {
    
    private static final Logger log = LoggerFactory.getLogger(Base64VlmTest.class);
    
    /**
     * 测试Base64编码图片的VLM识别功能
     */
    @Test
    public void testBase64ImageEncoding() {
        try {
            // 创建测试图片文件（这里使用一个小的测试图片）
            File testImageFile = createTestImageFile();
            
            if (testImageFile.exists()) {
                // 读取图片并编码为base64
                byte[] imageBytes = Files.readAllBytes(testImageFile.toPath());
                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                
                log.info("✅ Base64编码测试成功");
                log.info("📁 测试图片: {}", testImageFile.getName());
                log.info("📊 图片大小: {} KB", testImageFile.length() / 1024.0);
                log.info("🔢 Base64长度: {}", base64Image.length());
                log.info("🎯 Base64前缀: {}...", base64Image.substring(0, Math.min(50, base64Image.length())));
                
                // 验证Base64字符串是否有效
                if (isValidBase64(base64Image)) {
                    log.info("✅ Base64编码格式验证通过");
                } else {
                    log.error("❌ Base64编码格式验证失败");
                }
                
                // 模拟VLM prompt构建
                String imagePrompt = String.format(
                    "请以 Markdown 格式解析以下图像内容，尽可能提取标题、段落、列表和表格结构。\n\n![document](data:image/png;base64,%s)", 
                    base64Image.substring(0, Math.min(100, base64Image.length())) + "..."
                );
                
                log.info("📝 VLM Prompt示例构建成功:");
                log.info("   长度: {} 字符", imagePrompt.length());
                log.info("   包含base64图片: {}", imagePrompt.contains("data:image"));
                
                // 清理测试文件
                testImageFile.delete();
                
            } else {
                log.warn("⚠️ 测试图片文件不存在，跳过测试");
            }
            
        } catch (Exception e) {
            log.error("❌ Base64 VLM测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 验证Base64字符串格式
     */
    private boolean isValidBase64(String base64String) {
        try {
            Base64.getDecoder().decode(base64String);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 创建测试图片文件
     */
    private File createTestImageFile() throws IOException {
        // 创建一个简单的测试图片文件（1x1像素的PNG）
        byte[] minimalPng = {
            (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,  // PNG signature
            0x00, 0x00, 0x00, 0x0D,  // IHDR chunk length
            0x49, 0x48, 0x44, 0x52,  // IHDR
            0x00, 0x00, 0x00, 0x01,  // Width: 1
            0x00, 0x00, 0x00, 0x01,  // Height: 1
            0x08, 0x02,              // Bit depth: 8, Color type: 2 (RGB)
            0x00, 0x00, 0x00,        // Compression, filter, interlace
            (byte) 0x90, 0x77, 0x53, (byte) 0xDE,  // CRC
            0x00, 0x00, 0x00, 0x0C,  // IDAT chunk length
            0x49, 0x44, 0x41, 0x54,  // IDAT
            0x08, (byte) 0x99, 0x01, 0x01, 0x00, 0x00, (byte) 0xFF, (byte) 0xFF, 0x00, 0x00, 0x00, 0x02,  // Compressed data
            0x00, 0x01,  // CRC
            0x00, 0x00, 0x00, 0x00,  // IEND chunk length
            0x49, 0x45, 0x4E, 0x44,  // IEND
            (byte) 0xAE, 0x42, 0x60, (byte) 0x82   // CRC
        };
        
        File tempFile = File.createTempFile("test_image_", ".png");
        Files.write(tempFile.toPath(), minimalPng);
        
        log.info("📄 创建测试图片: {}", tempFile.getAbsolutePath());
        return tempFile;
    }
    
    /**
     * 测试VLM请求构建
     */
    @Test
    public void testVlmRequestBuild() {
        try {
            // 构建VLM转换请求
            VlmConversionRequest request = VlmConversionRequest.builder()
                    .vlmModel("qwen3-14b")  // 使用国产模型
                    .language("zh-CN")
                    .recognizeTables(true)
                    .recognizeFormulas(true)
                    .preserveLayout(true)
                    .enableParallelProcessing(false)
                    .customPrompt("请详细识别图像中的所有文本内容")
                    .build();
            
            log.info("✅ VLM请求构建成功");
            log.info("🤖 使用模型: {}", request.getVlmModel());
            log.info("🌏 输出语言: {}", request.getLanguage());
            log.info("📊 识别表格: {}", request.isRecognizeTables());
            log.info("🔢 识别公式: {}", request.isRecognizeFormulas());
            log.info("📐 保持布局: {}", request.isPreserveLayout());
            
            // 生成提示词
            String prompt = request.generatePrompt();
            log.info("📝 生成的提示词: {}", prompt);
            
        } catch (Exception e) {
            log.error("❌ VLM请求构建测试失败: {}", e.getMessage(), e);
        }
    }
}