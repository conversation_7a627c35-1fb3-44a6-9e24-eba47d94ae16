package com.talkweb.ai.converter.vlm;

import com.talkweb.ai.converter.vlm.config.VlmConfigurationManager;
import com.talkweb.ai.converter.vlm.monitor.VlmPerformanceMonitor;
import com.talkweb.ai.converter.vlm.util.VlmImageProcessor;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;

/**
 * VLM综合集成测试
 * 
 * 测试VLM系统的完整功能链路
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
public class VlmIntegrationTest {
    
    private static final Logger log = LoggerFactory.getLogger(VlmIntegrationTest.class);
    
    /**
     * 测试完整的VLM功能集成
     */
    @Test
    public void testCompleteVlmIntegration() {
        try {
            log.info("🚀 开始VLM系统综合集成测试");
            
            // 1. 测试配置管理器
            testConfigurationManager();
            
            // 2. 测试图像处理器
            testImageProcessor();
            
            // 3. 测试性能监控器
            testPerformanceMonitor();
            
            // 4. 测试完整流程
            testCompleteWorkflow();
            
            log.info("🎉 VLM系统综合集成测试全部通过！");
            
        } catch (Exception e) {
            log.error("❌ VLM系统集成测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试VLM配置管理器
     */
    private void testConfigurationManager() {
        log.info("📋 测试VLM配置管理器...");
        
        VlmConfigurationManager configManager = new VlmConfigurationManager();
        configManager.initialize();
        
        // 测试基本功能
        List<VlmConfigurationManager.VlmProvider> availableProviders = configManager.getAvailableProviders();
        List<VlmConfigurationManager.VlmProvider> domesticProviders = configManager.getDomesticProviders();
        List<VlmConfigurationManager.VlmProvider> internationalProviders = configManager.getInternationalProviders();
        
        log.info("✅ 配置管理器测试结果:");
        log.info("   可用提供商: {}", availableProviders.size());
        log.info("   国产提供商: {}", domesticProviders.size());
        log.info("   国际提供商: {}", internationalProviders.size());
        log.info("   推荐模型: {}", configManager.getRecommendedModel());
        log.info("   支持模型总数: {}", configManager.getAllSupportedModels().size());
        
        // 测试配置报告
        VlmConfigurationManager.VlmConfigurationReport report = configManager.generateConfigurationReport();
        log.info("📊 配置报告:");
        log.info("   默认模型: {}", report.getDefaultModel());
        log.info("   总提供商: {}", report.getTotalProviders());
        log.info("   可用提供商: {}", report.getAvailableProviders());
        
        // 测试配置指南
        String guide = configManager.generateConfigurationGuide();
        log.info("📖 配置指南长度: {} 字符", guide.length());
        
        assert configManager.getAllSupportedModels().size() > 0 : "必须有支持的模型";
        assert domesticProviders.size() > 0 : "必须有国产提供商";
        assert internationalProviders.size() > 0 : "必须有国际提供商";
        
        log.info("✅ 配置管理器测试通过");
    }
    
    /**
     * 测试图像处理器
     */
    private void testImageProcessor() throws IOException {
        log.info("🖼️ 测试VLM图像处理器...");
        
        // 创建测试图像
        File testImage = createTestImage();
        
        try {
            // 测试基础Base64转换
            String base64Simple = VlmImageProcessor.imageToBase64(testImage);
            assert base64Simple != null && !base64Simple.isEmpty() : "Base64编码不能为空";
            log.info("📊 简单Base64编码长度: {}", base64Simple.length());
            
            // 测试完整图像处理
            VlmImageProcessor.ProcessedImage processedImage = VlmImageProcessor.processImage(testImage);
            
            log.info("✅ 图像处理结果:");
            log.info("   原始尺寸: {}x{}", processedImage.getOriginalWidth(), processedImage.getOriginalHeight());
            log.info("   处理后尺寸: {}x{}", processedImage.getProcessedWidth(), processedImage.getProcessedHeight());
            log.info("   原始大小: {} bytes", processedImage.getOriginalSize());
            log.info("   Base64大小: {} 字符", processedImage.getBase64Size());
            log.info("   压缩比: {:.2f}%", processedImage.getCompressionRatio());
            log.info("   MIME类型: {}", processedImage.getMimeType());
            log.info("   是否调整大小: {}", processedImage.isResized());
            
            // 测试统计信息创建
            VlmImageProcessor.ImageProcessingStats stats = VlmImageProcessor.createStats(processedImage);
            log.info("📈 处理统计:");
            log.info("   原始尺寸: {}", stats.getOriginalDimensions());
            log.info("   处理后尺寸: {}", stats.getProcessedDimensions());
            log.info("   MIME类型: {}", stats.getMimeType());
            
            assert processedImage.getBase64Data() != null : "处理后的Base64数据不能为空";
            assert processedImage.getMimeType().startsWith("image/") : "MIME类型必须是图像";
            
            log.info("✅ 图像处理器测试通过");
            
        } finally {
            testImage.delete();
        }
    }
    
    /**
     * 测试性能监控器
     */
    private void testPerformanceMonitor() {
        log.info("📊 测试VLM性能监控器...");
        
        VlmPerformanceMonitor monitor = new VlmPerformanceMonitor();
        monitor.initialize();
        
        // 模拟一些请求
        String[] testModels = {"qwen3-14b", "gpt-4o", "claude-3-sonnet"};
        
        for (int i = 0; i < 10; i++) {
            String model = testModels[i % testModels.length];
            VlmPerformanceMonitor.VlmRequestTracker tracker = monitor.startRequest(model, "test-" + i);
            
            // 模拟处理时间
            try {
                Thread.sleep(10 + (int)(Math.random() * 20)); // 10-30ms
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 模拟不同的结果
            if (i % 5 == 0) {
                monitor.recordFailure(tracker, "timeout", "请求超时");
            } else if (i % 7 == 0) {
                monitor.recordFallback(tracker, "API密钥无效");
            } else {
                monitor.recordSuccess(tracker, 1000 + (int)(Math.random() * 2000));
            }
        }
        
        // 生成性能报告
        VlmPerformanceMonitor.VlmPerformanceReport report = monitor.generateReport();
        
        log.info("✅ 性能监控测试结果:");
        log.info("   总请求数: {}", report.getTotalRequests());
        log.info("   成功请求数: {}", report.getSuccessfulRequests());
        log.info("   失败请求数: {}", report.getFailedRequests());
        log.info("   回退请求数: {}", report.getFallbackRequests());
        log.info("   成功率: {:.2f}%", report.getSuccessRate());
        log.info("   平均响应时间: {:.2f}ms", report.getAverageResponseTime());
        log.info("   最小响应时间: {}ms", report.getMinResponseTime());
        log.info("   最大响应时间: {}ms", report.getMaxResponseTime());
        
        // 测试模型性能统计
        log.info("🤖 模型性能统计:");
        report.getModelPerformances().forEach((model, performance) -> {
            log.info("   {}: 请求={}, 成功率={:.1f}%, 平均响应时间={:.1f}ms", 
                    model, performance.getRequestCount(), 
                    performance.getSuccessRate(), performance.getAverageResponseTime());
        });
        
        // 测试统计摘要
        String summary = monitor.generateSummary();
        log.info("📝 统计摘要:\n{}", summary);
        
        assert report.getTotalRequests() == 10 : "总请求数应为10";
        assert report.getSuccessfulRequests() > 0 : "应该有成功的请求";
        assert report.getModelPerformances().size() == testModels.length : "应该有3个模型的统计";
        
        log.info("✅ 性能监控器测试通过");
    }
    
    /**
     * 测试完整工作流程
     */
    private void testCompleteWorkflow() throws IOException {
        log.info("🔄 测试VLM完整工作流程...");
        
        // 1. 初始化组件
        VlmConfigurationManager configManager = new VlmConfigurationManager();
        configManager.initialize();
        
        VlmPerformanceMonitor monitor = new VlmPerformanceMonitor();
        monitor.initialize();
        
        // 2. 创建测试图像
        File testImage = createTestImage();
        
        try {
            // 3. 选择推荐模型
            String recommendedModel = configManager.getRecommendedModel();
            log.info("📱 使用推荐模型: {}", recommendedModel);
            
            // 4. 开始性能监控
            VlmPerformanceMonitor.VlmRequestTracker tracker = 
                    monitor.startRequest(recommendedModel, "integration-test");
            
            // 5. 处理图像
            VlmImageProcessor.ProcessedImage processedImage = VlmImageProcessor.processImage(testImage);
            
            // 6. 模拟VLM调用（简化版本）
            String mockVlmPrompt = String.format(
                "请以 Markdown 格式解析以下图像内容，尽可能提取标题、段落、列表和表格结构。\n![doc](data:%s;base64,%s)",
                processedImage.getMimeType(), 
                processedImage.getBase64Data().substring(0, Math.min(100, processedImage.getBase64Data().length())) + "..."
            );
            
            // 7. 模拟成功响应
            String mockResponse = "# 文档标题\n\n这是一个测试文档的内容。\n\n- 列表项1\n- 列表项2\n\n| 列1 | 列2 |\n|-----|-----|\n| 数据1 | 数据2 |";
            
            // 8. 记录成功
            monitor.recordSuccess(tracker, mockResponse.length());
            
            // 9. 生成综合报告
            VlmConfigurationManager.VlmConfigurationReport configReport = configManager.generateConfigurationReport();
            VlmPerformanceMonitor.VlmPerformanceReport perfReport = monitor.generateReport();
            
            log.info("✅ 完整工作流程测试结果:");
            log.info("   使用模型: {}", recommendedModel);
            log.info("   图像处理: {}x{} -> Base64({} 字符)", 
                    processedImage.getOriginalWidth(), processedImage.getOriginalHeight(), 
                    processedImage.getBase64Size());
            log.info("   VLM提示长度: {}", mockVlmPrompt.length());
            log.info("   响应内容长度: {}", mockResponse.length());
            log.info("   可用提供商: {}/{}", configReport.getAvailableProviders(), configReport.getTotalProviders());
            log.info("   请求成功率: {:.1f}%", perfReport.getSuccessRate());
            
            assert processedImage.getBase64Data() != null : "图像处理必须成功";
            assert mockVlmPrompt.contains("data:image") : "VLM提示必须包含图像数据";
            assert perfReport.getTotalRequests() > 0 : "必须有监控数据";
            
            log.info("✅ 完整工作流程测试通过");
            
        } finally {
            testImage.delete();
        }
    }
    
    /**
     * 创建测试图像文件
     */
    private File createTestImage() throws IOException {
        // 创建一个简单的PNG图片（最小有效PNG）
        byte[] pngData = {
            (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,  // PNG signature
            0x00, 0x00, 0x00, 0x0D,  // IHDR chunk length
            0x49, 0x48, 0x44, 0x52,  // IHDR
            0x00, 0x00, 0x00, 0x01,  // Width: 1
            0x00, 0x00, 0x00, 0x01,  // Height: 1
            0x08, 0x02,              // Bit depth: 8, Color type: 2 (RGB)
            0x00, 0x00, 0x00,        // Compression, filter, interlace
            (byte) 0x90, 0x77, 0x53, (byte) 0xDE,  // CRC
            0x00, 0x00, 0x00, 0x0C,  // IDAT chunk length
            0x49, 0x44, 0x41, 0x54,  // IDAT
            0x08, (byte) 0x99, 0x01, 0x01, 0x00, 0x00, (byte) 0xFF, (byte) 0xFF, 0x00, 0x00, 0x00, 0x02,  // Compressed data
            0x00, 0x01,  // CRC
            0x00, 0x00, 0x00, 0x00,  // IEND chunk length
            0x49, 0x45, 0x4E, 0x44,  // IEND
            (byte) 0xAE, 0x42, 0x60, (byte) 0x82   // CRC
        };
        
        File tempFile = File.createTempFile("vlm_integration_test_", ".png");
        Files.write(tempFile.toPath(), pngData);
        
        return tempFile;
    }
    
    /**
     * 测试性能基准
     */
    @Test
    public void testPerformanceBenchmark() {
        log.info("⚡ 开始VLM性能基准测试...");
        
        VlmPerformanceMonitor monitor = new VlmPerformanceMonitor();
        monitor.initialize();
        
        int iterations = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            VlmPerformanceMonitor.VlmRequestTracker tracker = 
                    monitor.startRequest("qwen3-14b", "benchmark-" + i);
            
            // 模拟短处理时间
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            monitor.recordSuccess(tracker, 1000);
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        double requestsPerSecond = (double) iterations / (totalTime / 1000.0);
        
        VlmPerformanceMonitor.VlmPerformanceReport report = monitor.generateReport();
        
        log.info("⚡ 性能基准测试结果:");
        log.info("   测试迭代: {}", iterations);
        log.info("   总耗时: {}ms", totalTime);
        log.info("   每秒处理请求: {:.2f}", requestsPerSecond);
        log.info("   平均响应时间: {:.2f}ms", report.getAverageResponseTime());
        log.info("   成功率: {:.2f}%", report.getSuccessRate());
        
        assert requestsPerSecond > 10 : "每秒应该能处理至少10个请求";
        assert report.getSuccessRate() == 100.0 : "基准测试成功率应为100%";
        
        log.info("✅ 性能基准测试通过");
    }
}