package com.talkweb.ai.converter.vlm.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.io.TempDir;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VLM图像处理器优化功能测试
 * 
 * 测试流式处理、压缩优化、内存管理等优化功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@DisplayName("VLM图像处理优化测试")
class VlmImageProcessorOptimizationTest {

    @TempDir
    Path tempDir;

    private File testImageFile;
    private File largeImageFile;
    private File smallImageFile;

    @BeforeEach
    void setUp() throws IOException {
        // 创建测试图像文件
        testImageFile = createTestImage(tempDir, "test-image.png", 800, 600, BufferedImage.TYPE_INT_RGB);
        largeImageFile = createTestImage(tempDir, "large-image.jpg", 3000, 2000, BufferedImage.TYPE_INT_RGB);
        smallImageFile = createTestImage(tempDir, "small-image.png", 200, 150, BufferedImage.TYPE_INT_RGB);
    }

    private File createTestImage(Path directory, String filename, int width, int height, int imageType) throws IOException {
        BufferedImage image = new BufferedImage(width, height, imageType);
        Graphics2D g2d = image.createGraphics();
        
        // 绘制一个简单的测试图案
        g2d.setColor(Color.BLUE);
        g2d.fillRect(0, 0, width, height);
        g2d.setColor(Color.WHITE);
        g2d.drawString("Test Image " + width + "x" + height, 10, 30);
        g2d.dispose();
        
        File imageFile = directory.resolve(filename).toFile();
        String format = filename.endsWith(".png") ? "png" : "jpg";
        ImageIO.write(image, format, imageFile);
        
        return imageFile;
    }

    @Test
    @DisplayName("测试流式Base64编码")
    void testStreamingBase64Encoding() throws IOException {
        // 测试流式编码
        String streamingResult = VlmImageProcessor.imageToBase64Streaming(testImageFile);
        
        // 测试标准编码作为对比
        String standardResult = VlmImageProcessor.imageToBase64(testImageFile);
        
        // 结果应该一致
        assertEquals(standardResult, streamingResult);
        
        // 验证Base64格式正确性
        assertNotNull(streamingResult);
        assertFalse(streamingResult.isEmpty());
        
        // 验证可以正确解码
        byte[] decodedBytes = Base64.getDecoder().decode(streamingResult);
        assertTrue(decodedBytes.length > 0);
    }

    @Test
    @DisplayName("测试图像处理和压缩")
    void testImageProcessingAndCompression() throws IOException {
        VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(largeImageFile);
        
        // 验证基本属性
        assertNotNull(result);
        assertNotNull(result.getBase64Data());
        assertNotNull(result.getMimeType());
        
        // 验证尺寸调整
        assertTrue(result.isResized(), "大图像应该被调整大小");
        assertTrue(result.getProcessedWidth() <= 2048, "处理后宽度应该不超过2048");
        assertTrue(result.getProcessedHeight() <= 2048, "处理后高度应该不超过2048");
        
        // 验证原始信息保留
        assertEquals(3000, result.getOriginalWidth());
        assertEquals(2000, result.getOriginalHeight());
        assertTrue(result.getOriginalSize() > 0);
        
        // 验证压缩效果
        assertTrue(result.getBase64Size() > 0);
        double compressionRatio = result.getCompressionRatio();
        assertTrue(compressionRatio > 0 && compressionRatio <= 100);
    }

    @Test
    @DisplayName("测试小图像处理（无需调整大小）")
    void testSmallImageProcessing() throws IOException {
        VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(smallImageFile);
        
        // 小图像不应该被调整大小
        assertFalse(result.isResized(), "小图像不应该被调整大小");
        assertEquals(result.getOriginalWidth(), result.getProcessedWidth());
        assertEquals(result.getOriginalHeight(), result.getProcessedHeight());
        
        // 验证处理结果
        assertNotNull(result.getBase64Data());
        assertTrue(result.getBase64Size() > 0);
    }

    @Test
    @DisplayName("测试MIME类型检测")
    void testMimeTypeDetection() throws IOException {
        // 测试PNG文件
        VlmImageProcessor.ProcessedImage pngResult = VlmImageProcessor.processImage(testImageFile);
        assertEquals("image/png", pngResult.getMimeType());
        
        // 测试JPEG文件
        VlmImageProcessor.ProcessedImage jpegResult = VlmImageProcessor.processImage(largeImageFile);
        assertEquals("image/jpeg", jpegResult.getMimeType());
    }

    @Test
    @DisplayName("测试图像处理性能")
    @Timeout(10)
    void testImageProcessingPerformance() throws IOException {
        long startTime = System.currentTimeMillis();
        
        // 处理多个图像
        int iterations = 10;
        for (int i = 0; i < iterations; i++) {
            VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(testImageFile);
            assertNotNull(result);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能：10次处理应该在合理时间内完成
        assertTrue(duration < 5000, "图像处理性能不达标: " + duration + "ms");
        
        double avgTimePerImage = (double) duration / iterations;
        assertTrue(avgTimePerImage < 500, "平均每张图像处理时间过长: " + avgTimePerImage + "ms");
    }

    @Test
    @DisplayName("测试并发图像处理")
    @Timeout(15)
    void testConcurrentImageProcessing() throws InterruptedException {
        int threadCount = 5;
        int imagesPerThread = 3;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    for (int j = 0; j < imagesPerThread; j++) {
                        VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(testImageFile);
                        assertNotNull(result);
                        assertNotNull(result.getBase64Data());
                    }
                } catch (IOException e) {
                    fail("并发图像处理失败: " + e.getMessage());
                }
            }, executor);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(10, TimeUnit.SECONDS));
    }

    @Test
    @DisplayName("测试内存使用优化")
    void testMemoryUsageOptimization() throws IOException {
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收并记录初始内存
        runtime.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 处理大量图像
        for (int i = 0; i < 20; i++) {
            VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(largeImageFile);
            assertNotNull(result);
            // 不保持引用，让GC可以回收
        }
        
        // 再次强制垃圾回收
        runtime.gc();
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        
        long memoryGrowth = finalMemory - initialMemory;
        
        // 内存增长应该在合理范围内（考虑到处理了20张大图）
        assertTrue(memoryGrowth < 100 * 1024 * 1024, // 100MB
                  "内存增长过多: " + (memoryGrowth / 1024 / 1024) + " MB");
    }

    @Test
    @DisplayName("测试图像统计信息")
    void testImageProcessingStats() throws IOException {
        VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(testImageFile);
        VlmImageProcessor.ImageProcessingStats stats = VlmImageProcessor.createStats(result);
        
        // 验证统计信息
        assertNotNull(stats);
        assertNotNull(stats.getOriginalDimensions());
        assertNotNull(stats.getProcessedDimensions());
        assertNotNull(stats.getMimeType());
        
        assertTrue(stats.getOriginalSize() > 0);
        assertTrue(stats.getBase64Size() > 0);
        assertTrue(stats.getCompressionRatio() > 0);
        
        // 验证尺寸格式
        assertTrue(stats.getOriginalDimensions().matches("\\d+x\\d+"));
        assertTrue(stats.getProcessedDimensions().matches("\\d+x\\d+"));
    }

    @Test
    @DisplayName("测试文件验证功能")
    void testFileValidation() throws IOException {
        // 测试不存在的文件
        File nonExistentFile = tempDir.resolve("non-existent.png").toFile();
        assertThrows(FileNotFoundException.class, () -> {
            VlmImageProcessor.processImage(nonExistentFile);
        });
        
        // 测试空文件
        File emptyFile = tempDir.resolve("empty.png").toFile();
        emptyFile.createNewFile();
        assertThrows(IOException.class, () -> {
            VlmImageProcessor.processImage(emptyFile);
        });
        
        // 测试不支持的格式
        File unsupportedFile = tempDir.resolve("test.txt").toFile();
        Files.write(unsupportedFile.toPath(), "test content".getBytes());
        assertThrows(IOException.class, () -> {
            VlmImageProcessor.processImage(unsupportedFile);
        });
    }

    @Test
    @DisplayName("测试大文件处理限制")
    void testLargeFileLimit() throws IOException {
        // 创建一个超大的虚拟文件（模拟）
        File largeFile = tempDir.resolve("too-large.png").toFile();
        
        // 创建一个正常大小的图像，然后测试大小限制逻辑
        // 实际测试中，我们验证现有的大小检查逻辑
        VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(largeImageFile);
        assertNotNull(result);
        
        // 验证大文件被正确调整大小
        assertTrue(result.getProcessedWidth() <= 2048);
        assertTrue(result.getProcessedHeight() <= 2048);
    }

    @RepeatedTest(3)
    @DisplayName("测试处理结果一致性（重复测试）")
    void testProcessingConsistency() throws IOException {
        VlmImageProcessor.ProcessedImage result1 = VlmImageProcessor.processImage(testImageFile);
        VlmImageProcessor.ProcessedImage result2 = VlmImageProcessor.processImage(testImageFile);
        
        // 相同文件的处理结果应该一致
        assertEquals(result1.getOriginalWidth(), result2.getOriginalWidth());
        assertEquals(result1.getOriginalHeight(), result2.getOriginalHeight());
        assertEquals(result1.getProcessedWidth(), result2.getProcessedWidth());
        assertEquals(result1.getProcessedHeight(), result2.getProcessedHeight());
        assertEquals(result1.getMimeType(), result2.getMimeType());
        assertEquals(result1.isResized(), result2.isResized());
        
        // Base64内容应该相同
        assertEquals(result1.getBase64Data(), result2.getBase64Data());
    }

    @Test
    @DisplayName("测试Base64编码质量")
    void testBase64EncodingQuality() throws IOException {
        String base64Result = VlmImageProcessor.imageToBase64(testImageFile);
        
        // 验证Base64格式
        assertNotNull(base64Result);
        assertFalse(base64Result.isEmpty());
        assertTrue(base64Result.matches("^[A-Za-z0-9+/]*={0,2}$"));
        
        // 验证解码后的数据
        byte[] decodedData = Base64.getDecoder().decode(base64Result);
        assertTrue(decodedData.length > 0);
        
        // 验证解码后可以重新创建图像
        ByteArrayInputStream bis = new ByteArrayInputStream(decodedData);
        BufferedImage decodedImage = ImageIO.read(bis);
        assertNotNull(decodedImage);
        assertTrue(decodedImage.getWidth() > 0);
        assertTrue(decodedImage.getHeight() > 0);
    }

    @Test
    @DisplayName("测试压缩比计算准确性")
    void testCompressionRatioAccuracy() throws IOException {
        VlmImageProcessor.ProcessedImage result = VlmImageProcessor.processImage(largeImageFile);
        
        double compressionRatio = result.getCompressionRatio();
        
        // 压缩比应该在合理范围内
        assertTrue(compressionRatio > 0, "压缩比应该大于0");
        assertTrue(compressionRatio <= 200, "压缩比不应该过大"); // 考虑到Base64编码会增加大小
        
        // 对于JPEG格式，通常会有压缩效果
        if (result.getMimeType().equals("image/jpeg")) {
            // JPEG压缩通常会减小文件大小
            assertNotNull(compressionRatio);
        }
    }
}