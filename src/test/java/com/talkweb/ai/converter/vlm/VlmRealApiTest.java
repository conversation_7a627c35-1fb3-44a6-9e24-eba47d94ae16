package com.talkweb.ai.converter.vlm;

import com.talkweb.ai.converter.vlm.model.VlmConversionRequest;
import com.talkweb.ai.converter.vlm.model.VlmConversionResult;
import com.talkweb.ai.converter.vlm.service.impl.VisionLanguageModelService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * VLM真实API测试
 * 
 * 需要配置有效的API密钥才能运行此测试
 * 
 * 环境变量配置：
 * - QWEN_API_KEY: 通义千问API密钥
 * - OPENAI_API_KEY: OpenAI API密钥
 * - ZHIPU_API_KEY: 智谱AI API密钥
 */
@SpringBootTest
@ActiveProfiles("test")
public class VlmRealApiTest {
    
    private static final Logger log = LoggerFactory.getLogger(VlmRealApiTest.class);
    
    @Autowired
    private VisionLanguageModelService visionLanguageModelService;
    
    /**
     * 测试是否有有效的API密钥配置
     */
    private boolean hasValidApiKey() {
        return System.getenv("QWEN_API_KEY") != null ||
               System.getenv("OPENAI_API_KEY") != null ||
               System.getenv("ZHIPU_API_KEY") != null ||
               System.getenv("BAICHUAN_API_KEY") != null;
    }
    
    @Test
    @EnabledIf("hasValidApiKey")
    public void testVlmServiceAvailability() throws Exception {
        // 测试VLM服务可用性
        CompletableFuture<Boolean> availability = visionLanguageModelService.checkServiceAvailability();
        Boolean isAvailable = availability.get();
        
        log.info("VLM service availability: {}", isAvailable);
        
        if (isAvailable) {
            log.info("✅ VLM service is available and ready for use");
        } else {
            log.warn("⚠️ VLM service is not available, will use fallback mode");
        }
    }
    
    @Test
    @EnabledIf("hasValidApiKey")
    public void testVlmImageRecognitionWithMockImage() throws Exception {
        // 创建一个简单的测试文件（模拟）
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File mockImageFile = new File(tempDir, "test-image.png");
        
        // 如果测试图像不存在，跳过测试
        if (!mockImageFile.exists()) {
            log.info("No test image found at {}, skipping real VLM test", mockImageFile.getPath());
            return;
        }
        
        // 创建VLM转换请求
        VlmConversionRequest request = VlmConversionRequest.builder()
                .vlmModel("qwen3-14b")
                .language("zh-CN")
                .recognizeTables(true)
                .recognizeFormulas(true)
                .preserveLayout(true)
                .customPrompt("请识别图像中的文本内容并转换为Markdown格式，保持原有结构。")
                .build();
        
        // 执行VLM识别
        List<File> imageFiles = List.of(mockImageFile);
        
        log.info("Testing VLM recognition with model: {}", request.getVlmModel());
        
        CompletableFuture<VlmConversionResult.VlmRecognitionResult> result = 
                visionLanguageModelService.recognizeImages(imageFiles, request);
        
        VlmConversionResult.VlmRecognitionResult recognitionResult = result.get();
        
        log.info("VLM recognition completed");
        log.info("Success: {}", recognitionResult.isSuccess());
        log.info("Processing time: {} ms", recognitionResult.getTotalRecognitionTimeMs());
        
        if (recognitionResult.isSuccess()) {
            log.info("✅ VLM recognition successful");
            log.info("Combined markdown length: {}", recognitionResult.getCombinedMarkdown().length());
            log.info("Page results count: {}", recognitionResult.getPageResults().size());
        } else {
            log.warn("⚠️ VLM recognition failed: {}", recognitionResult.getErrorMessage());
        }
    }
    
    @Test
    public void testVlmFallbackMode() throws Exception {
        // 测试回退模式（不需要API密钥）
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File mockImageFile = new File(tempDir, "fallback-test.png");
        
        // 创建一个空的测试文件
        if (!mockImageFile.exists()) {
            mockImageFile.createNewFile();
            mockImageFile.deleteOnExit();
        }
        
        // 创建VLM转换请求
        VlmConversionRequest request = VlmConversionRequest.builder()
                .vlmModel("qwen3-14b")
                .language("zh-CN")
                .build();
        
        List<File> imageFiles = List.of(mockImageFile);
        
        log.info("Testing VLM fallback mode");
        
        CompletableFuture<VlmConversionResult.VlmRecognitionResult> result = 
                visionLanguageModelService.recognizeImages(imageFiles, request);
        
        VlmConversionResult.VlmRecognitionResult recognitionResult = result.get();
        
        log.info("Fallback mode test completed");
        log.info("Success: {}", recognitionResult.isSuccess());
        
        // 即使在回退模式下，也应该返回有意义的内容
        if (recognitionResult.isSuccess()) {
            String content = recognitionResult.getCombinedMarkdown();
            log.info("Fallback content generated, length: {}", content.length());
            
            // 验证回退内容包含配置说明
            assert content.contains("配置说明") : "回退内容应包含配置说明";
            assert content.contains("QWEN_API_KEY") : "回退内容应包含API密钥说明";
            
            log.info("✅ Fallback mode working correctly");
        }
    }
    
    @Test
    public void testVlmSupportedModels() {
        // 测试支持的模型列表
        List<String> supportedModels = visionLanguageModelService.getSupportedModels();
        
        log.info("Supported VLM models: {}", supportedModels);
        
        // 验证国产大模型包含在支持列表中
        assert supportedModels.contains("qwen3-14b") : "应支持通义千问模型";
        assert supportedModels.contains("qwen-vl-plus") : "应支持通义千问VL增强模型";
        assert supportedModels.contains("chatglm-6b") : "应支持智谱AI模型";
        assert supportedModels.contains("baichuan2-13b") : "应支持百川AI模型";
        
        log.info("✅ All expected models are supported");
    }
    
    @Test
    public void testVlmStatistics() {
        // 测试VLM统计信息
        VlmConversionResult.VlmStatistics statistics = visionLanguageModelService.getStatistics();
        
        log.info("VLM Statistics:");
        log.info("  Total requests: {}", statistics.getTotalRequests());
        log.info("  Successful requests: {}", statistics.getSuccessfulRequests());
        log.info("  Failed requests: {}", statistics.getFailedRequests());
        log.info("  Model usage: {}", statistics.getModelUsageCount());
        
        // 统计信息应该是有效的
        assert statistics.getTotalRequests() >= 0 : "总请求数应为非负数";
        assert statistics.getSuccessfulRequests() >= 0 : "成功请求数应为非负数";
        assert statistics.getFailedRequests() >= 0 : "失败请求数应为非负数";
        
        log.info("✅ Statistics are valid");
    }
}