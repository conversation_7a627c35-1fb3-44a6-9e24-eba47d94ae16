package com.talkweb.ai.converter.vlm;

import com.talkweb.ai.converter.vlm.cache.VlmCacheService;
import com.talkweb.ai.converter.vlm.config.VlmConfigurationManager;
import com.talkweb.ai.converter.vlm.monitor.VlmPerformanceMonitor;
import com.talkweb.ai.converter.vlm.service.VlmBatchProcessingService;
import com.talkweb.ai.converter.vlm.service.impl.VisionLanguageModelService;
import com.talkweb.ai.converter.vlm.util.VlmImageProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VLM系统集成测试
 * 
 * 测试整个VLM系统的端到端功能，包括缓存、批量处理、性能监控等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
public class VlmSystemIntegrationTest {
    
    @Autowired
    private VisionLanguageModelService vlmService;
    
    @Autowired
    private VlmCacheService cacheService;
    
    @Autowired
    private VlmConfigurationManager configManager;
    
    @Autowired
    private VlmPerformanceMonitor performanceMonitor;
    
    @Autowired
    private VlmBatchProcessingService batchProcessingService;
    
    private File testImageFile;
    private Path tempDir;
    
    @BeforeEach
    public void setUp() throws IOException {
        // 创建临时目录
        tempDir = Files.createTempDirectory("vlm_test");
        
        // 创建测试图像文件
        testImageFile = createTestImage("test_image.png");
        
        // 清理缓存
        cacheService.clearAllCache();
        
        // 重置性能统计
        performanceMonitor.resetStatistics();
    }
    
    @Test
    public void testConfigurationManager() {
        System.out.println("\n=== 测试配置管理器 ===");
        
        // 测试获取配置报告
        VlmConfigurationManager.VlmConfigurationReport report = configManager.generateConfigurationReport();
        assertNotNull(report);
        assertTrue(report.getTotalProviders() > 0);
        assertNotNull(report.getRecommendedModel());
        
        System.out.println("✅ 配置管理器测试结果:");
        System.out.println("   可用提供商: " + report.getAvailableProviders());
        System.out.println("   国产提供商: " + report.getDomesticProviders());
        System.out.println("   国际提供商: " + report.getInternationalProviders());
        System.out.println("   推荐模型: " + report.getRecommendedModel());
        System.out.println("   支持模型总数: " + report.getTotalModels());
        
        // 测试配置指导
        String guide = configManager.generateConfigurationGuide();
        assertNotNull(guide);
        assertTrue(guide.contains("VLM模型配置指南"));
        
        // 测试提供商获取
        List<VlmConfigurationManager.VlmProvider> availableProviders = configManager.getAvailableProviders();
        assertNotNull(availableProviders);
        
        List<VlmConfigurationManager.VlmProvider> domesticProviders = configManager.getDomesticProviders();
        assertNotNull(domesticProviders);
        assertEquals(4, domesticProviders.size()); // qwen, zhipu, baichuan, internlm
        
        List<VlmConfigurationManager.VlmProvider> internationalProviders = configManager.getInternationalProviders();
        assertNotNull(internationalProviders);
        assertEquals(2, internationalProviders.size()); // openai, anthropic
        
        // 测试模型映射
        List<String> allModels = configManager.getAllSupportedModels();
        assertNotNull(allModels);
        assertTrue(allModels.size() >= 13); // 至少13个模型
        
        System.out.println("   全部支持模型: " + allModels);
    }
    
    @Test
    public void testImageProcessor() throws IOException {
        System.out.println("\n=== 测试图像处理器 ===");
        
        // 测试Base64转换
        String base64 = VlmImageProcessor.imageToBase64(testImageFile);
        assertNotNull(base64);
        assertTrue(base64.length() > 0);
        
        // 验证Base64数据
        byte[] decoded = Base64.getDecoder().decode(base64);
        assertTrue(decoded.length > 0);
        
        // 测试完整图像处理
        VlmImageProcessor.ProcessedImage processedImage = VlmImageProcessor.processImage(testImageFile);
        assertNotNull(processedImage);
        assertNotNull(processedImage.getBase64Data());
        assertTrue(processedImage.getBase64Data().length() > 0);
        
        // 测试处理统计
        VlmImageProcessor.ImageProcessingStats stats = VlmImageProcessor.createStats(processedImage);
        assertNotNull(stats);
        assertTrue(processedImage.getOriginalSize() > 0);
        assertTrue(processedImage.getBase64Size() > 0);
        assertTrue(processedImage.getOriginalWidth() > 0);
        assertTrue(processedImage.getOriginalHeight() > 0);
        
        System.out.println("✅ 图像处理器测试结果:");
        System.out.println("   Base64长度: " + base64.length());
        System.out.println("   原始尺寸: " + processedImage.getOriginalWidth() + "x" + processedImage.getOriginalHeight());
        System.out.println("   处理后尺寸: " + processedImage.getProcessedWidth() + "x" + processedImage.getProcessedHeight());
        System.out.println("   压缩比: " + String.format("%.2f%%", processedImage.getCompressionRatio()));
        System.out.println("   文件大小: 原始=" + processedImage.getOriginalSize() + " Base64=" + processedImage.getBase64Size());
    }
    
    @Test
    public void testCacheService() throws IOException {
        System.out.println("\n=== 测试缓存服务 ===");
        
        // 测试Base64缓存
        String base64_1 = cacheService.getOrComputeBase64(testImageFile);
        String base64_2 = cacheService.getOrComputeBase64(testImageFile);
        assertEquals(base64_1, base64_2); // 第二次应该从缓存获取
        
        // 测试VLM结果缓存
        String prompt = "测试提示词";
        String model = "test-model";
        String testResult = "测试结果";
        
        // 首次查询应该返回null
        String cachedResult = cacheService.getOrComputeVlmResult(testImageFile, prompt, model);
        assertNull(cachedResult);
        
        // 缓存结果
        cacheService.cacheVlmResult(testImageFile, prompt, model, testResult);
        
        // 再次查询应该返回缓存的结果
        String retrievedResult = cacheService.getOrComputeVlmResult(testImageFile, prompt, model);
        assertEquals(testResult, retrievedResult);
        
        // 测试缓存统计
        VlmCacheService.CacheStatistics stats = cacheService.getCacheStatistics();
        assertNotNull(stats);
        assertTrue(stats.getBase64CacheSize() > 0);
        assertTrue(stats.getVlmResultCacheSize() > 0);
        
        System.out.println("✅ 缓存服务测试结果:");
        System.out.println("   Base64缓存大小: " + stats.getBase64CacheSize());
        System.out.println("   VLM结果缓存大小: " + stats.getVlmResultCacheSize());
        System.out.println("   Base64命中率: " + String.format("%.1f%%", stats.getBase64HitRate()));
        System.out.println("   VLM命中率: " + String.format("%.1f%%", stats.getVlmHitRate()));
        System.out.println("   总内存使用: " + formatBytes(stats.getTotalMemoryUsage()));
        
        // 测试缓存清理
        cacheService.clearAllCache();
        VlmCacheService.CacheStatistics clearedStats = cacheService.getCacheStatistics();
        assertEquals(0, clearedStats.getBase64CacheSize());
        assertEquals(0, clearedStats.getVlmResultCacheSize());
    }
    
    @Test
    public void testPerformanceMonitor() throws InterruptedException {
        System.out.println("\n=== 测试性能监控器 ===");
        
        String testModel = "test-model";
        int iterations = 100;
        
        long startTime = System.currentTimeMillis();
        
        // 模拟请求
        for (int i = 0; i < iterations; i++) {
            VlmPerformanceMonitor.VlmRequestTracker tracker = 
                    performanceMonitor.startRequest(testModel, "request-" + i);
            
            // 模拟处理时间
            Thread.sleep(1);
            
            if (i % 10 == 0) {
                // 模拟一些失败
                performanceMonitor.recordFailure(tracker, "test_error", "测试错误");
            } else {
                // 模拟成功
                performanceMonitor.recordSuccess(tracker, 1000 + i);
            }
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        
        // 获取性能报告
        VlmPerformanceMonitor.VlmPerformanceReport report = performanceMonitor.generateReport();
        assertNotNull(report);
        assertEquals(iterations, report.getTotalRequests());
        assertTrue(report.getSuccessfulRequests() > 0);
        assertTrue(report.getFailedRequests() > 0);
        assertTrue(report.getSuccessRate() > 0);
        assertTrue(report.getAverageResponseTime() > 0);
        
        // 获取简要摘要
        String summary = performanceMonitor.generateSummary();
        assertNotNull(summary);
        assertTrue(summary.contains("VLM服务统计摘要"));
        
        System.out.println("⚡ 性能基准测试结果:");
        System.out.println("   测试迭代: " + iterations);
        System.out.println("   总耗时: " + totalTime + "ms");
        System.out.println("   每秒处理请求: " + String.format("%.2f", iterations * 1000.0 / totalTime));
        System.out.println("   平均响应时间: " + String.format("%.2f", report.getAverageResponseTime()) + "ms");
        System.out.println("   成功率: " + String.format("%.2f%%", report.getSuccessRate()));
        System.out.println("   失败率: " + String.format("%.2f%%", report.getFailureRate()));
    }
    
    @Test
    public void testBatchProcessingService() throws Exception {
        System.out.println("\n=== 测试批量处理服务 ===");
        
        // 创建多个测试图像
        List<File> imageFiles = Arrays.asList(
                createTestImage("batch_test_1.png"),
                createTestImage("batch_test_2.png"),
                createTestImage("batch_test_3.png")
        );
        
        // 创建请求参数 - 使用配置管理器推荐的模型
        com.talkweb.ai.converter.vlm.model.VlmConversionRequest request = 
                com.talkweb.ai.converter.vlm.model.VlmConversionRequest.builder()
                        .vlmModel(configManager.getRecommendedModel())
                        .language("zh-CN")
                        .build();
        
        // 执行批量处理
        CompletableFuture<VlmBatchProcessingService.BatchProcessingResult> future = 
                batchProcessingService.batchProcessImages(imageFiles, request);
        
        // 等待完成（设置超时）
        VlmBatchProcessingService.BatchProcessingResult result = future.get(30, TimeUnit.SECONDS);
        
        assertNotNull(result);
        assertEquals(imageFiles.size(), result.getTotalCount());
        assertNotNull(result.getBatchId());
        assertTrue(result.getTotalProcessingTime() > 0);
        assertNotNull(result.getModelUsed());
        assertNotNull(result.getCombinedMarkdown());
        
        System.out.println("📦 批量处理测试结果:");
        System.out.println("   批次ID: " + result.getBatchId());
        System.out.println("   总文件数: " + result.getTotalCount());
        System.out.println("   成功数: " + result.getSuccessfulCount());
        System.out.println("   失败数: " + result.getFailedCount());
        System.out.println("   成功率: " + String.format("%.1f%%", result.getSuccessRate()));
        System.out.println("   平均处理时间: " + String.format("%.1f", result.getAverageProcessingTime()) + "ms");
        System.out.println("   使用模型: " + result.getModelUsed());
        
        // 测试任务状态查询
        List<VlmBatchProcessingService.BatchTask> activeTasks = batchProcessingService.getActiveTasks();
        assertNotNull(activeTasks);
        
        // 测试特定任务状态
        VlmBatchProcessingService.BatchTask task = batchProcessingService.getBatchTaskStatus(result.getBatchId());
        if (task != null) {
            assertEquals(VlmBatchProcessingService.BatchTaskStatus.COMPLETED, task.getStatus());
            assertEquals(100.0, task.getProgress(), 0.1);
        }
        
        // 清理测试文件
        imageFiles.forEach(file -> {
            if (file.exists()) file.delete();
        });
    }
    
    @Test
    public void testSystemIntegration() throws IOException {
        System.out.println("\n=== 测试系统集成 ===");
        
        // 测试所有组件是否正确协作
        
        // 1. 配置检查
        String recommendedModel = configManager.getRecommendedModel();
        assertNotNull(recommendedModel);
        
        // 2. 图像处理
        VlmImageProcessor.ProcessedImage processedImage = VlmImageProcessor.processImage(testImageFile);
        assertNotNull(processedImage);
        
        // 3. 缓存操作
        String base64 = cacheService.getOrComputeBase64(testImageFile);
        assertNotNull(base64);
        
        // 4. 性能监控
        VlmPerformanceMonitor.VlmRequestTracker tracker = 
                performanceMonitor.startRequest(recommendedModel, "integration-test");
        performanceMonitor.recordSuccess(tracker, 1000);
        
        VlmPerformanceMonitor.VlmPerformanceReport perfReport = performanceMonitor.generateReport();
        assertTrue(perfReport.getTotalRequests() > 0);
        
        // 5. 缓存统计
        VlmCacheService.CacheStatistics cacheStats = cacheService.getCacheStatistics();
        assertTrue(cacheStats.getBase64CacheSize() > 0);
        
        System.out.println("🔗 系统集成测试通过:");
        System.out.println("   配置管理: ✅");
        System.out.println("   图像处理: ✅");
        System.out.println("   缓存服务: ✅");
        System.out.println("   性能监控: ✅");
        System.out.println("   组件协作: ✅");
    }
    
    /**
     * 创建测试图像文件
     */
    private File createTestImage(String filename) throws IOException {
        // 创建一个简单的测试图像
        int width = 200;
        int height = 100;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        Graphics2D g2d = image.createGraphics();
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);
        g2d.setColor(Color.BLUE);
        g2d.drawString("VLM Test Image", 20, 50);
        g2d.dispose();
        
        File imageFile = tempDir.resolve(filename).toFile();
        javax.imageio.ImageIO.write(image, "PNG", imageFile);
        
        return imageFile;
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024));
    }
}