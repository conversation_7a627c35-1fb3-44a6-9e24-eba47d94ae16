package com.talkweb.ai.converter.vlm;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Base64;

/**
 * 简单的Base64 VLM功能测试（无需Spring上下文）
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
public class SimpleBase64Test {
    
    private static final Logger log = LoggerFactory.getLogger(SimpleBase64Test.class);
    
    /**
     * 测试Base64编码图片功能
     */
    @Test
    public void testBase64ImageEncoding() {
        try {
            // 创建测试图片文件
            File testImageFile = createTestImageFile();
            
            if (testImageFile.exists()) {
                // 读取图片并编码为base64
                byte[] imageBytes = Files.readAllBytes(testImageFile.toPath());
                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                
                log.info("✅ Base64编码测试成功");
                log.info("📁 测试图片: {}", testImageFile.getName());
                log.info("📊 图片大小: {} KB", testImageFile.length() / 1024.0);
                log.info("🔢 Base64长度: {}", base64Image.length());
                
                // 模拟VLM prompt构建（按您的要求格式）
                String vlmPrompt = String.format(
                    "请以 Markdown 格式解析以下图像内容，尽可能提取标题、段落、列表和表格结构。\n![doc](data:image/png;base64,%s)", 
                    base64Image
                );
                
                log.info("📝 VLM Prompt构建成功:");
                log.info("   总长度: {} 字符", vlmPrompt.length());
                log.info("   包含data:image标识: {}", vlmPrompt.contains("data:image/png;base64,"));
                
                // 验证提取的部分
                String promptPrefix = "请以 Markdown 格式解析以下图像内容，尽可能提取标题、段落、列表和表格结构。";
                String imageDataUrl = "![doc](data:image/png;base64," + base64Image + ")";
                
                log.info("📋 Prompt前缀: {}", promptPrefix);
                log.info("🖼️ 图像Data URL长度: {}", imageDataUrl.length());
                
                // 模拟使用ChatClient调用的代码段
                String simulatedCode = String.format(
                    "String md = client.prompt()\n" +
                    "    .user(\"%s\\n![doc](data:image/png;base64,\" + Base64.getEncoder().encodeToString(img) + \")\")\n" +
                    "    .call()\n" +
                    "    .content();",
                    promptPrefix
                );
                
                log.info("💻 模拟代码示例:\n{}", simulatedCode);
                
                // 验证Base64编码/解码一致性
                byte[] decodedBytes = Base64.getDecoder().decode(base64Image);
                boolean isConsistent = java.util.Arrays.equals(imageBytes, decodedBytes);
                log.info("🔄 编码解码一致性验证: {}", isConsistent ? "✅ 通过" : "❌ 失败");
                
                // 清理测试文件
                testImageFile.delete();
                log.info("🧹 测试文件已清理");
                
            } else {
                log.warn("⚠️ 测试图片文件不存在，跳过测试");
            }
            
        } catch (Exception e) {
            log.error("❌ Base64 VLM测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 创建最小测试图片文件（1x1像素PNG）
     */
    private File createTestImageFile() throws IOException {
        // 创建一个最小的有效PNG文件
        byte[] minimalPng = {
            (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,  // PNG signature
            0x00, 0x00, 0x00, 0x0D,  // IHDR chunk length
            0x49, 0x48, 0x44, 0x52,  // IHDR
            0x00, 0x00, 0x00, 0x01,  // Width: 1
            0x00, 0x00, 0x00, 0x01,  // Height: 1
            0x08, 0x02,              // Bit depth: 8, Color type: 2 (RGB)
            0x00, 0x00, 0x00,        // Compression, filter, interlace
            (byte) 0x90, 0x77, 0x53, (byte) 0xDE,  // CRC
            0x00, 0x00, 0x00, 0x0C,  // IDAT chunk length
            0x49, 0x44, 0x41, 0x54,  // IDAT
            0x08, (byte) 0x99, 0x01, 0x01, 0x00, 0x00, (byte) 0xFF, (byte) 0xFF, 0x00, 0x00, 0x00, 0x02,  // Compressed data
            0x00, 0x01,  // CRC
            0x00, 0x00, 0x00, 0x00,  // IEND chunk length
            0x49, 0x45, 0x4E, 0x44,  // IEND
            (byte) 0xAE, 0x42, 0x60, (byte) 0x82   // CRC
        };
        
        File tempFile = File.createTempFile("base64_test_", ".png");
        Files.write(tempFile.toPath(), minimalPng);
        
        log.info("📄 创建测试图片: {} ({} bytes)", tempFile.getName(), minimalPng.length);
        return tempFile;
    }
}