# VLM PDF转换使用指南

## 概述

VLM (Vision Language Model) 是一种先进的多模态AI技术，能够同时理解图像和文本内容。本系统集成了VLM功能，为PDF文档转换提供了比传统OCR更准确、更智能的处理能力。

## 功能特点

### 🎯 高精度识别
- **智能文本识别**: 使用最先进的视觉语言模型，识别准确率远超传统OCR
- **版面理解**: 智能识别文档结构，保持原始版面布局
- **多语言支持**: 支持中文、英文、日文、韩文等多种语言

### 🔧 专业功能
- **表格识别**: 精确识别复杂表格结构并转换为Markdown格式
- **公式处理**: 识别数学公式和科学符号
- **图像描述**: 对文档中的图片提供智能描述
- **结构保持**: 维持标题、段落、列表等文档结构

## 使用方法

### 1. 命令行使用

#### 基本用法
```bash
# 使用默认VLM设置转换PDF
java -jar doc-converter.jar convert -i document.pdf --vlm

# 指定输出目录
java -jar doc-converter.jar convert -i document.pdf -o output/ --vlm
```

#### 高级配置
```bash
# 自定义VLM参数
java -jar doc-converter.jar convert -i document.pdf \
  --vlm \
  --vlm-model gpt-4-vision-preview \
  --vlm-dpi 300 \
  --vlm-format PNG \
  --vlm-language zh-CN

# 批量处理目录
java -jar doc-converter.jar convert -i /path/to/pdfs/ \
  --vlm \
  --parallel \
  --threads 4
```

#### 参数说明
- `--vlm`: 启用VLM转换模式
- `--vlm-model`: 指定VLM模型（默认: gpt-4-vision-preview）
- `--vlm-dpi`: 图像DPI设置（默认: 300）
- `--vlm-format`: 图像格式（PNG/JPEG，默认: PNG）
- `--vlm-language`: 输出语言（默认: zh-CN）

### 2. Web API使用

#### 文件上传转换
```bash
curl -X POST http://localhost:8080/api/v1/files/upload \
  -F "file=@document.pdf" \
  -F "useVlm=true" \
  -F "vlmModel=gpt-4-vision-preview" \
  -F "vlmDpi=300" \
  -F "vlmLanguage=zh-CN"
```

#### JavaScript示例
```javascript
const formData = new FormData();
formData.append('file', pdfFile);
formData.append('useVlm', 'true');
formData.append('vlmModel', 'gpt-4-vision-preview');
formData.append('vlmDpi', '300');
formData.append('vlmLanguage', 'zh-CN');

fetch('/api/v1/files/upload', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('转换任务创建成功:', data);
    // 轮询任务状态
    checkTaskStatus(data.data.taskId);
});
```

### 3. 配置文件

#### 启用VLM功能
在`application.properties`中添加：
```properties
# 启用VLM功能
vlm.enabled=true

# 国产大模型API密钥配置（推荐）
spring.ai.qwen.api-key=${QWEN_API_KEY}

# 或使用OpenAI
spring.ai.openai.api-key=${OPENAI_API_KEY}

# 智谱AI配置
spring.ai.zhipu.api-key=${ZHIPU_API_KEY}

# 百川AI配置
spring.ai.baichuan.api-key=${BAICHUAN_API_KEY}
```

#### 高级配置
使用`application-vlm.yml`进行详细配置：
```yaml
vlm:
  enabled: true
  default-model: qwen3-14b  # 使用国产大模型作为默认选项
  default-dpi: 300
  performance:
    max-concurrent-tasks: 5
    timeout-ms: 120000
  quality-control:
    confidence-threshold: 0.8
    preserve-layout: true

# 国产大模型配置示例
spring:
  ai:
    qwen:
      api-key: ${QWEN_API_KEY}
      base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
    zhipu:
      api-key: ${ZHIPU_API_KEY}
      base-url: https://open.bigmodel.cn/api/paas/v4
```

## 支持的模型

### OpenAI模型
- `gpt-4-vision-preview`: 最强大的视觉理解能力
- `gpt-4o`: 平衡性能和成本
- `gpt-4o-mini`: 快速轻量级处理

### Anthropic模型
- `claude-3-opus`: 最高质量的文档理解
- `claude-3-sonnet`: 平衡性能和速度
- `claude-3-haiku`: 快速处理

### 国产大模型 🇨🇳
- `qwen3-14b`: 阿里通义千问，中文文档识别能力强
- `qwen-vl-plus`: 通义千问VL增强版
- `qwen-vl-max`: 通义千问VL最强版本
- `chatglm-6b`: 智谱AI清言，支持多轮对话
- `baichuan2-13b`: 百川智能，专业中文处理
- `internlm-xcomposer2-7b`: 上海AI实验室多模态模型

## 最佳实践

### 🎯 选择合适的参数

#### 文档类型优化
```bash
# 中文学术论文（推荐国产模型）
--vlm-model qwen3-14b --vlm-dpi 300 --vlm-format PNG --vlm-language zh-CN

# 英文学术论文（高质量，包含公式和表格）
--vlm-model gpt-4-vision-preview --vlm-dpi 300 --vlm-format PNG

# 商业文档（快速处理）
--vlm-model qwen-vl-plus --vlm-dpi 200 --vlm-format JPEG

# 多语言文档
--vlm-model qwen-vl-max --vlm-language zh-CN
```

#### 性能优化
```bash
# 大批量处理（推荐国产模型，性价比高）
--parallel --threads 4 --vlm-model qwen3-14b

# 高质量单文档
--vlm-model qwen-vl-max --vlm-dpi 600

# 成本优化（使用国产模型）
--vlm-model qwen3-14b --vlm-dpi 300
```

### 📋 质量检查

#### 结果验证
1. **置信度检查**: VLM返回的置信度分数
2. **内容长度**: 确保识别到足够的内容
3. **结构完整性**: 检查标题、段落等结构是否正确

#### 错误处理
```bash
# 启用OCR回退（VLM失败时自动使用传统OCR）
--vlm --fallback-to-ocr
```

## 性能考虑

### 💰 成本优化
- **模型选择**: 根据需求选择合适的模型
- **DPI设置**: 较低DPI可降低处理成本
- **批量处理**: 减少API调用次数

### ⚡ 速度优化
- **并行处理**: 启用多线程处理
- **缓存机制**: 利用内置缓存避免重复处理
- **轻量级模型**: 使用mini版本模型加快处理

### 📊 监控指标
```bash
# 查看处理统计
curl http://localhost:8080/api/v1/vlm/statistics

# 监控系统状态
curl http://localhost:8080/api/v1/system/status
```

## 故障排除

### 常见问题

#### 1. API密钥问题
```bash
# 检查环境变量
echo $OPENAI_API_KEY

# 在配置文件中设置
spring.ai.openai.api-key=your-actual-api-key
```

#### 2. 内存不足
```bash
# 增加JVM内存
java -Xmx4g -jar doc-converter.jar

# 减少并发数
--vlm-concurrent-tasks 2
```

#### 3. 网络超时
```yaml
vlm:
  performance:
    timeout-ms: 300000  # 5分钟
```

#### 4. 质量问题
```bash
# 提高图像质量
--vlm-dpi 600 --vlm-format PNG

# 使用更强模型
--vlm-model gpt-4-vision-preview
```

## 示例场景

### 📚 中文学术论文处理
```bash
java -jar doc-converter.jar convert -i research_paper.pdf \
  --vlm \
  --vlm-model qwen3-14b \
  --vlm-dpi 300 \
  --vlm-language zh-CN \
  --preserve-structure
```

### 💼 商业报告处理
```bash
java -jar doc-converter.jar convert -i business_report.pdf \
  --vlm \
  --vlm-model qwen-vl-plus \
  --vlm-language zh-CN \
  --recognize-tables
```

### 🔬 技术文档处理
```bash
java -jar doc-converter.jar convert -i technical_manual.pdf \
  --vlm \
  --vlm-model qwen-vl-max \
  --vlm-dpi 400 \
  --recognize-formulas
```

### 🌐 英文文档处理
```bash
java -jar doc-converter.jar convert -i english_document.pdf \
  --vlm \
  --vlm-model gpt-4-vision-preview \
  --vlm-language en-US \
  --vlm-dpi 300
```

## 进阶功能

### 🚀 真实VLM接口实现

从v1.0.0+开始，系统已集成真正的Spring AI 1.0.0 VLM接口：

#### API调用流程
1. **图像预处理**: 自动验证格式、大小和质量
2. **多模态调用**: 使用Spring AI ChatClient进行文本+图像处理
3. **智能回退**: API失败时自动回退到说明模式
4. **结果优化**: 自动后处理提升识别质量

#### 支持的VLM接口
```yaml
# 通义千问VLM (推荐)
spring.ai.qwen.api-key: ${QWEN_API_KEY}
spring.ai.qwen.base-url: https://dashscope.aliyuncs.com/compatible-mode/v1

# OpenAI GPT-4 Vision
spring.ai.openai.api-key: ${OPENAI_API_KEY}

# 智谱AI GLM-4V
spring.ai.zhipu.api-key: ${ZHIPU_API_KEY}
spring.ai.zhipu.base-url: https://open.bigmodel.cn/api/paas/v4

# 百川AI VLM
spring.ai.baichuan.api-key: ${BAICHUAN_API_KEY}
spring.ai.baichuan.base-url: https://api.baichuan-ai.com/v1
```

#### 智能回退机制
当VLM API不可用时，系统会：
- 🔍 **自动检测**: API密钥错误、网络问题、模型不支持等
- 📋 **生成说明**: 提供配置指导和占位内容
- 📊 **记录统计**: 跟踪成功率和失败原因
- 🔄 **无缝切换**: 不中断整体转换流程

### 自定义提示词
```yaml
vlm:
  custom-prompts:
    default: "请准确识别文档内容并转换为Markdown格式，保持原有结构。"
    table-focused: "重点关注表格结构的准确识别和转换。"
    formula-focused: "请特别注意数学公式和科学符号的识别。"
    chinese-optimized: "请以中文输出，识别文档中的所有文本、表格和公式，保持原始排版结构。"
```

### 结果后处理
```yaml
vlm:
  post-processing:
    enable-spell-check: true
    enable-format-cleanup: true
    enable-structure-validation: true
```

## 技术支持

### 日志调试
```yaml
logging:
  level:
    com.talkweb.ai.converter.vlm: DEBUG
```

### 性能分析
```bash
# 启用详细统计
--vlm --enable-statistics

# 查看性能报告
curl http://localhost:8080/api/v1/vlm/performance
```

---

## 联系我们

如果您在使用VLM功能时遇到问题，请：

1. 查看日志文件：`logs/vlm-conversion.log`
2. 检查配置文件：`application-vlm.yml`
3. 提交Issue或联系技术支持

VLM功能让PDF转换变得更加智能和准确，助力您的文档处理工作！