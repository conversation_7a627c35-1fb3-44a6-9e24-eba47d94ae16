# VLM国产大模型演示指南

## 概述

本指南展示如何使用国产大模型（如通义千问、智谱AI、百川AI）进行VLM PDF转换，专门针对中文文档处理进行了优化。

## 快速开始

### 1. 环境配置

```bash
# 配置通义千问API密钥
export QWEN_API_KEY="your-qwen-api-key"

# 配置智谱AI API密钥  
export ZHIPU_API_KEY="your-zhipu-api-key"

# 配置百川AI API密钥
export BAICHUAN_API_KEY="your-baichuan-api-key"
```

### 2. 基本使用

#### 通义千问 (推荐)
```bash
# 使用通义千问转换中文PDF
java -jar doc-converter.jar convert -i 中文文档.pdf \
  --vlm \
  --vlm-model qwen3-14b \
  --vlm-language zh-CN

# 批量处理
java -jar doc-converter.jar convert -i /path/to/chinese/docs/ \
  --vlm \
  --vlm-model qwen-vl-plus \
  --parallel --threads 4
```

#### 智谱AI
```bash
# 使用智谱AI处理学术论文
java -jar doc-converter.jar convert -i 学术论文.pdf \
  --vlm \
  --vlm-model chatglm-6b \
  --vlm-dpi 300 \
  --recognize-formulas
```

#### 百川AI
```bash
# 使用百川AI处理商业文档
java -jar doc-converter.jar convert -i 商业报告.pdf \
  --vlm \
  --vlm-model baichuan2-13b \
  --recognize-tables
```

### 3. Web API使用

```bash
# 使用通义千问通过API转换
curl -X POST http://localhost:8080/api/v1/files/upload \
  -F "file=@中文文档.pdf" \
  -F "useVlm=true" \
  -F "vlmModel=qwen3-14b" \
  -F "vlmLanguage=zh-CN" \
  -F "vlmDpi=300"
```

## 国产模型特色功能

### 🇨🇳 中文优化

```yaml
# application-vlm.yml 中文优化配置
vlm:
  default-model: qwen3-14b  # 默认使用通义千问
  default-language: zh-CN   # 中文输出
  quality-control:
    preserve-layout: true   # 保持中文版面
    enable-table-recognition: true  # 中文表格识别
```

### 📊 性价比对比

| 模型 | 中文理解 | 处理速度 | 成本 | 推荐场景 |
|------|----------|----------|------|----------|
| qwen3-14b | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 通用中文文档 |
| qwen-vl-plus | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 高质量文档 |
| chatglm-6b | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 学术文档 |
| baichuan2-13b | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 商业文档 |

### 🚀 最佳实践

#### 学术论文处理
```bash
# 中文学术论文 - 最佳配置
java -jar doc-converter.jar convert -i 学术论文.pdf \
  --vlm-model qwen-vl-max \
  --vlm-dpi 400 \
  --vlm-language zh-CN \
  --preserve-structure \
  --recognize-formulas \
  --recognize-tables
```

#### 商业文档处理
```bash
# 商业报告 - 快速处理
java -jar doc-converter.jar convert -i 商业报告.pdf \
  --vlm-model qwen3-14b \
  --vlm-dpi 200 \
  --vlm-language zh-CN \
  --recognize-tables
```

#### 技术文档处理
```bash
# 技术手册 - 平衡质量和速度
java -jar doc-converter.jar convert -i 技术手册.pdf \
  --vlm-model qwen-vl-plus \
  --vlm-dpi 300 \
  --vlm-language zh-CN \
  --preserve-layout
```

## 配置文件示例

### 通义千问配置
```yaml
spring:
  ai:
    qwen:
      api-key: ${QWEN_API_KEY}
      base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
      chat:
        options:
          model: qwen3-14b
          temperature: 0.1
          max-tokens: 8192
```

### 智谱AI配置
```yaml
spring:
  ai:
    zhipu:
      api-key: ${ZHIPU_API_KEY}
      base-url: https://open.bigmodel.cn/api/paas/v4
      chat:
        options:
          model: chatglm-6b
          temperature: 0.1
          max-tokens: 4096
```

## 故障排除

### 常见问题

#### 1. API密钥错误
```bash
# 检查环境变量
echo $QWEN_API_KEY
echo $ZHIPU_API_KEY
echo $BAICHUAN_API_KEY
```

#### 2. 网络连接问题
```yaml
# 增加超时时间
vlm:
  performance:
    timeout-ms: 300000  # 5分钟
```

#### 3. 中文识别效果不佳
```bash
# 提高图像质量
--vlm-dpi 400 --vlm-format PNG

# 使用更强的模型
--vlm-model qwen-vl-max
```

## 性能监控

```bash
# 查看VLM统计信息
curl http://localhost:8080/api/v1/vlm/statistics

# 监控国产模型使用情况
curl http://localhost:8080/api/v1/vlm/model-usage
```

## 总结

国产大模型在中文文档处理方面具有显著优势：

- **语言理解**: 对中文语义理解更准确
- **成本优势**: 相比国外模型更具性价比
- **本土支持**: 更好的技术支持和服务
- **合规性**: 符合国内数据安全要求

推荐配置：
- **日常使用**: qwen3-14b
- **高质量需求**: qwen-vl-max  
- **大批量处理**: qwen3-14b + 并行处理
- **预算受限**: chatglm-6b

---

🇨🇳 **让中文文档处理更智能，让国产AI更强大！**