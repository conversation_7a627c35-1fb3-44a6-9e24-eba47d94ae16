#!/bin/bash

# 测试结果统计脚本
# 分析 Maven Surefire 测试报告

WORKSPACE_DIR="test-reports-workspace"
OUTPUT_FILE="test-results-summary.md"

echo "# Maven 测试执行结果统计报告" > $OUTPUT_FILE
echo "" >> $OUTPUT_FILE
echo "执行时间: $(date)" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

# 初始化计数器
total_tests=0
total_failures=0
total_errors=0
total_skipped=0
total_success=0

# 存储失败的测试
failed_tests_file=".failed_tests.tmp"
error_tests_file=".error_tests.tmp"
skipped_tests_file=".skipped_tests.tmp"

# 清理临时文件
rm -f "$failed_tests_file" "$error_tests_file" "$skipped_tests_file"

echo "## 1. 测试统计概览" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

# 分析 XML 报告文件
if [ -d "$WORKSPACE_DIR" ]; then
    echo "正在分析 XML 测试报告..."
    
    for xml_file in $WORKSPACE_DIR/TEST-*.xml; do
        if [ -f "$xml_file" ]; then
            # 提取测试类名
            test_class=$(basename "$xml_file" .xml | sed 's/TEST-//')
            
            # 使用 grep 和 sed 提取测试统计信息
            if grep -q "testsuite" "$xml_file"; then
                tests=$(grep "testsuite" "$xml_file" | head -1 | sed -n 's/.*tests="\([^"]*\)".*/\1/p')
                failures=$(grep "testsuite" "$xml_file" | head -1 | sed -n 's/.*failures="\([^"]*\)".*/\1/p')
                errors=$(grep "testsuite" "$xml_file" | head -1 | sed -n 's/.*errors="\([^"]*\)".*/\1/p')
                skipped=$(grep "testsuite" "$xml_file" | head -1 | sed -n 's/.*skipped="\([^"]*\)".*/\1/p')
                
                # 处理空值，设为0
                tests=${tests:-0}
                failures=${failures:-0}
                errors=${errors:-0}
                skipped=${skipped:-0}
                
                # 累加总数
                total_tests=$((total_tests + tests))
                total_failures=$((total_failures + failures))
                total_errors=$((total_errors + errors))
                total_skipped=$((total_skipped + skipped))
                
                # 记录失败和错误的测试类
                if [ "$failures" -gt 0 ]; then
                    echo "$test_class:$failures" >> "$failed_tests_file"
                fi
                
                if [ "$errors" -gt 0 ]; then
                    echo "$test_class:$errors" >> "$error_tests_file"
                fi
                
                if [ "$skipped" -gt 0 ]; then
                    echo "$test_class:$skipped" >> "$skipped_tests_file"
                fi
            fi
        fi
    done
    
    # 计算成功测试数
    total_success=$((total_tests - total_failures - total_errors - total_skipped))
    
    # 输出统计结果
    echo "| 统计项 | 数量 | 百分比 |" >> $OUTPUT_FILE
    echo "|--------|------|-------|" >> $OUTPUT_FILE
    echo "| 总测试数 | $total_tests | 100.0% |" >> $OUTPUT_FILE
    
    if [ $total_tests -gt 0 ]; then
        success_percent=$(echo "scale=1; $total_success * 100 / $total_tests" | bc 2>/dev/null || echo "0.0")
        failure_percent=$(echo "scale=1; $total_failures * 100 / $total_tests" | bc 2>/dev/null || echo "0.0")
        error_percent=$(echo "scale=1; $total_errors * 100 / $total_tests" | bc 2>/dev/null || echo "0.0")
        skipped_percent=$(echo "scale=1; $total_skipped * 100 / $total_tests" | bc 2>/dev/null || echo "0.0")
    else
        success_percent="0.0"
        failure_percent="0.0"
        error_percent="0.0"
        skipped_percent="0.0"
    fi
    
    echo "| 成功测试 | $total_success | ${success_percent}% |" >> $OUTPUT_FILE
    echo "| 失败测试 | $total_failures | ${failure_percent}% |" >> $OUTPUT_FILE
    echo "| 错误测试 | $total_errors | ${error_percent}% |" >> $OUTPUT_FILE
    echo "| 跳过测试 | $total_skipped | ${skipped_percent}% |" >> $OUTPUT_FILE
    
    echo "" >> $OUTPUT_FILE
    
    # 失败测试清单
    echo "## 2. 失败测试清单" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    if [ -f "$failed_tests_file" ] && [ -s "$failed_tests_file" ]; then
        echo "### 2.1 测试失败 (Failures)" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        echo "| 测试类 | 失败数量 |" >> $OUTPUT_FILE
        echo "|--------|----------|" >> $OUTPUT_FILE
        
        while IFS=':' read -r test_class failure_count; do
            echo "| $test_class | $failure_count |" >> $OUTPUT_FILE
        done < "$failed_tests_file"
        echo "" >> $OUTPUT_FILE
    else
        echo "### 2.1 测试失败 (Failures)" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        echo "✅ 没有测试失败" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
    fi
    
    if [ -f "$error_tests_file" ] && [ -s "$error_tests_file" ]; then
        echo "### 2.2 测试错误 (Errors)" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        echo "| 测试类 | 错误数量 |" >> $OUTPUT_FILE
        echo "|--------|----------|" >> $OUTPUT_FILE
        
        while IFS=':' read -r test_class error_count; do
            echo "| $test_class | $error_count |" >> $OUTPUT_FILE
        done < "$error_tests_file"
        echo "" >> $OUTPUT_FILE
    else
        echo "### 2.2 测试错误 (Errors)" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        echo "✅ 没有测试错误" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
    fi
    
    if [ -f "$skipped_tests_file" ] && [ -s "$skipped_tests_file" ]; then
        echo "### 2.3 跳过测试 (Skipped)" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        echo "| 测试类 | 跳过数量 |" >> $OUTPUT_FILE
        echo "|--------|----------|" >> $OUTPUT_FILE
        
        while IFS=':' read -r test_class skipped_count; do
            echo "| $test_class | $skipped_count |" >> $OUTPUT_FILE
        done < "$skipped_tests_file"
        echo "" >> $OUTPUT_FILE
    else
        echo "### 2.3 跳过测试 (Skipped)" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        echo "✅ 没有跳过的测试" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
    fi
    
    # 详细失败信息（从对应的txt文件中提取）
    echo "## 3. 详细失败信息" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    # 处理失败测试详情
    if [ -f "$failed_tests_file" ] && [ -s "$failed_tests_file" ]; then
        echo "### 3.1 失败测试详情" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        
        while IFS=':' read -r test_class failure_count; do
            echo "#### $test_class (失败: $failure_count)" >> $OUTPUT_FILE
            echo "" >> $OUTPUT_FILE
            txt_file="$WORKSPACE_DIR/${test_class}.txt"
            if [ -f "$txt_file" ]; then
                echo '```' >> $OUTPUT_FILE
                tail -30 "$txt_file" >> $OUTPUT_FILE
                echo '```' >> $OUTPUT_FILE
            else
                echo "⚠️ 无法找到对应的详细日志文件" >> $OUTPUT_FILE
            fi
            echo "" >> $OUTPUT_FILE
        done < "$failed_tests_file"
    fi
    
    # 处理错误测试详情
    if [ -f "$error_tests_file" ] && [ -s "$error_tests_file" ]; then
        echo "### 3.2 错误测试详情" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
        
        while IFS=':' read -r test_class error_count; do
            echo "#### $test_class (错误: $error_count)" >> $OUTPUT_FILE
            echo "" >> $OUTPUT_FILE
            txt_file="$WORKSPACE_DIR/${test_class}.txt"
            if [ -f "$txt_file" ]; then
                echo '```' >> $OUTPUT_FILE
                tail -30 "$txt_file" >> $OUTPUT_FILE
                echo '```' >> $OUTPUT_FILE
            else
                echo "⚠️ 无法找到对应的详细日志文件" >> $OUTPUT_FILE
            fi
            echo "" >> $OUTPUT_FILE
        done < "$error_tests_file"
    fi
    
    # 检查匹配模式的测试（Controller和Plugin相关）
    controller_plugin_failures=0
    echo "### 3.3 Controller/Plugin 相关测试失败汇总" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    # 检查失败测试中的Controller/Plugin
    if [ -f "$failed_tests_file" ] && [ -s "$failed_tests_file" ]; then
        while IFS=':' read -r test_class failure_count; do
            if echo "$test_class" | grep -E "(Controller.*Test|Plugin.*Test)" > /dev/null; then
                controller_plugin_failures=$((controller_plugin_failures + 1))
                echo "- **$test_class**: $failure_count 个失败" >> $OUTPUT_FILE
            fi
        done < "$failed_tests_file"
    fi
    
    # 检查错误测试中的Controller/Plugin
    if [ -f "$error_tests_file" ] && [ -s "$error_tests_file" ]; then
        while IFS=':' read -r test_class error_count; do
            if echo "$test_class" | grep -E "(Controller.*Test|Plugin.*Test)" > /dev/null; then
                controller_plugin_failures=$((controller_plugin_failures + 1))
                echo "- **$test_class**: $error_count 个错误" >> $OUTPUT_FILE
            fi
        done < "$error_tests_file"
    fi
    
    if [ $controller_plugin_failures -eq 0 ]; then
        echo "✅ 没有 Controller/Plugin 相关测试失败" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
    else
        echo "" >> $OUTPUT_FILE
        echo "⚠️ **共发现 $controller_plugin_failures 个 Controller/Plugin 相关测试问题**" >> $OUTPUT_FILE
        echo "" >> $OUTPUT_FILE
    fi
    
    echo "## 4. 工作区文件清单" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    echo "测试报告文件已复制到: \`$WORKSPACE_DIR\`" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    echo "### 4.1 XML 报告文件" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    xml_count=$(ls $WORKSPACE_DIR/TEST-*.xml 2>/dev/null | wc -l)
    echo "- XML 文件总数: $xml_count" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
    echo "### 4.2 TXT 报告文件" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    txt_count=$(ls $WORKSPACE_DIR/*.txt 2>/dev/null | wc -l)
    echo "- TXT 文件总数: $txt_count" >> $OUTPUT_FILE
    echo "" >> $OUTPUT_FILE
    
else
    echo "❌ 工作区目录不存在: $WORKSPACE_DIR" >> $OUTPUT_FILE
fi

echo "## 5. 总结" >> $OUTPUT_FILE
echo "" >> $OUTPUT_FILE

if [ $total_failures -eq 0 ] && [ $total_errors -eq 0 ]; then
    echo "🎉 **所有测试均通过！**" >> $OUTPUT_FILE
elif [ $controller_plugin_failures -gt 0 ]; then
    echo "⚠️  **发现 Controller/Plugin 相关测试失败，需要重点关注**" >> $OUTPUT_FILE
else
    echo "⚠️  **存在测试失败，但非 Controller/Plugin 相关**" >> $OUTPUT_FILE
fi

echo "" >> $OUTPUT_FILE
echo "---" >> $OUTPUT_FILE
echo "报告生成完成时间: $(date)" >> $OUTPUT_FILE

echo "测试结果统计完成！"
echo "报告已保存到: $OUTPUT_FILE"
echo ""
echo "快速统计:"
echo "  总测试数: $total_tests"
echo "  成功: $total_success"
echo "  失败: $total_failures" 
echo "  错误: $total_errors"
echo "  跳过: $total_skipped"
