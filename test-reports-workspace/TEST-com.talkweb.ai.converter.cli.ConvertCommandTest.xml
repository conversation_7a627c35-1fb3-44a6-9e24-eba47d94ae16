<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" version="3.0.2" name="com.talkweb.ai.converter.cli.ConvertCommandTest" time="2.289" tests="2" errors="0" skipped="0" failures="0">
  <properties>
    <property name="socksProxyHost" value="127.0.0.1"/>
    <property name="http.proxyHost" value="127.0.0.1"/>
    <property name="java.specification.version" value="24"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Downloads/重庆园区/src/search/converter/target/test-classes:/Users/<USER>/Downloads/重庆园区/src/search/converter/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.2/spring-boot-starter-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.2/spring-boot-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.2/spring-boot-autoconfigure-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.2/spring-boot-starter-logging-3.5.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.4/snakeyaml-2.4.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.2/spring-boot-starter-web-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.2/spring-boot-starter-json-3.5.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.2/spring-boot-starter-tomcat-3.5.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.2/spring-boot-starter-data-jpa-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.2/spring-boot-starter-jdbc-3.5.2.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.2/spring-boot-starter-websocket-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.2/spring-boot-starter-thymeleaf-3.5.2.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.2/spring-boot-starter-validation-3.5.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-openai/1.0.0/spring-ai-openai-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-model/1.0.0/spring-ai-model-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-commons/1.0.0/spring-ai-commons-1.0.0.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.3/context-propagation-1.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-template-st/1.0.0/spring-ai-template-st-1.0.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-retry/1.0.0/spring-ai-retry-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/retry/spring-retry/2.0.12/spring-retry-2.0.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webflux/6.2.8/spring-webflux-6.2.8.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.37.0/jsonschema-generator-4.37.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.37.0/jsonschema-module-jackson-4.37.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-starter-model-openai/1.0.0/spring-ai-starter-model-openai-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-openai/1.0.0/spring-ai-autoconfigure-model-openai-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-tool/1.0.0/spring-ai-autoconfigure-model-tool-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-retry/1.0.0/spring-ai-autoconfigure-retry-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-chat-observation/1.0.0/spring-ai-autoconfigure-model-chat-observation-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-embedding-observation/1.0.0/spring-ai-autoconfigure-model-embedding-observation-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-image-observation/1.0.0/spring-ai-autoconfigure-model-image-observation-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-chat-memory/1.0.0/spring-ai-autoconfigure-model-chat-memory-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-client-chat/1.0.0/spring-ai-client-chat-1.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.19.1/jackson-module-jsonSchema-2.19.1.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.25/swagger-annotations-2.2.25.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.37.0/jsonschema-module-swagger-2-4.37.0.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mcp/1.0.0/spring-ai-mcp-1.0.0.jar:/Users/<USER>/.m2/repository/io/modelcontextprotocol/sdk/mcp/0.10.0/mcp-0.10.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-starter-mcp-server-webmvc/1.0.0/spring-ai-starter-mcp-server-webmvc-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-mcp-server/1.0.0/spring-ai-autoconfigure-mcp-server-1.0.0.jar:/Users/<USER>/.m2/repository/io/modelcontextprotocol/sdk/mcp-spring-webmvc/0.10.0/mcp-spring-webmvc-0.10.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-chat-client/1.0.0/spring-ai-autoconfigure-model-chat-client-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.2/spring-boot-starter-actuator-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.2/spring-boot-actuator-autoconfigure-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.2/spring-boot-actuator-3.5.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.7/picocli-4.7.7.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/5.4.1/poi-ooxml-5.4.1.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/5.4.1/poi-5.4.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.3/SparseBitSet-1.3.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-lite/5.4.1/poi-ooxml-lite-5.4.1.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/5.3.0/xmlbeans-5.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.18.0/commons-io-2.18.0.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.08/curvesapi-1.08.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-scratchpad/5.4.1/poi-scratchpad-5.4.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.5/pdfbox-3.0.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.5/pdfbox-io-3.0.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.5/fontbox-3.0.5.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.3.5/commons-logging-1.3.5.jar:/Users/<USER>/.m2/repository/org/jsoup/jsoup/1.20.1/jsoup-1.20.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.16.0/tess4j-5.16.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.17.0/jna-5.17.0.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.5/pdfbox-tools-3.0.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.5/pdfbox-debugger-3.0.5.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk18on/1.80/bcpkix-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcutil-jdk18on/1.80/bcutil-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.21.1/lept4j-1.21.1.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/odftoolkit/odfdom-java/0.12.0/odfdom-java-0.12.0.jar:/Users/<USER>/.m2/repository/xerces/xercesImpl/2.12.2/xercesImpl-2.12.2.jar:/Users/<USER>/.m2/repository/xalan/serializer/2.7.3/serializer-2.7.3.jar:/Users/<USER>/.m2/repository/org/apache/jena/jena-core/4.10.0/jena-core-4.10.0.jar:/Users/<USER>/.m2/repository/org/apache/jena/jena-base/4.10.0/jena-base-4.10.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.10.0/commons-csv-1.10.0.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/3.2.1/caffeine-3.2.1.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar:/Users/<USER>/.m2/repository/com/github/andrewoma/dexx/collection/0.7/collection-0.7.jar:/Users/<USER>/.m2/repository/org/apache/jena/jena-iri/4.10.0/jena-iri-4.10.0.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.5.0/commons-cli-1.5.0.jar:/Users/<USER>/.m2/repository/org/roaringbitmap/RoaringBitmap/1.0.0/RoaringBitmap-1.0.0.jar:/Users/<USER>/.m2/repository/net/rootdev/java-rdfa/1.0.0-BETA1/java-rdfa-1.0.0-BETA1.jar:/Users/<USER>/.m2/repository/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/Users/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/org/json/json/20231013/json-20231013.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/2.0.17/slf4j-simple-2.0.17.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.33.0/checker-qual-3.33.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.2/spring-boot-starter-test-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.2/spring-boot-test-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.2/spring-boot-test-autoconfigure-3.5.2.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:"/>
    <property name="https.proxyPort" value="1083"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="24"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/.sdkman/candidates/java/24-graal/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Downloads/重庆园区/src/search/converter/target/surefire/surefirebooter-20250630011147690_620.jar /Users/<USER>/Downloads/重庆园区/src/search/converter/target/surefire 2025-06-30T01-11-47_320-jvmRun1 surefire-20250630011147690_618tmp surefire_63-20250630011147690_619tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Downloads/重庆园区/src/search/converter/target/test-classes:/Users/<USER>/Downloads/重庆园区/src/search/converter/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.5.2/spring-boot-starter-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.5.2/spring-boot-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.5.2/spring-boot-autoconfigure-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.5.2/spring-boot-starter-logging-3.5.2.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.5.18/logback-classic-1.5.18.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.5.18/logback-core-1.5.18.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.24.3/log4j-to-slf4j-2.24.3.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.17/jul-to-slf4j-2.0.17.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/6.2.8/spring-core-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/6.2.8/spring-jcl-6.2.8.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.4/snakeyaml-2.4.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.38/lombok-1.18.38.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.5.2/spring-boot-starter-web-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.5.2/spring-boot-starter-json-3.5.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.19.1/jackson-datatype-jdk8-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.19.1/jackson-datatype-jsr310-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.19.1/jackson-module-parameter-names-2.19.1.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.5.2/spring-boot-starter-tomcat-3.5.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.42/tomcat-embed-core-10.1.42.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.42/tomcat-embed-websocket-10.1.42.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/6.2.8/spring-web-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/6.2.8/spring-beans-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/6.2.8/spring-webmvc-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/6.2.8/spring-aop-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/6.2.8/spring-expression-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.5.2/spring-boot-starter-data-jpa-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.5.2/spring-boot-starter-jdbc-3.5.2.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/6.3.0/HikariCP-6.3.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/6.2.8/spring-jdbc-6.2.8.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.18.Final/hibernate-core-6.6.18.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.7.0/classmate-1.7.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.17.6/byte-buddy-1.17.6.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.5/jaxb-runtime-4.0.5.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.5/jaxb-core-4.0.5.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.2/angus-activation-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.5/txw2-4.0.5.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.5.1/spring-data-jpa-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.5.1/spring-data-commons-3.5.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/6.2.8/spring-orm-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/6.2.8/spring-tx-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/6.2.8/spring-aspects-6.2.8.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.24/aspectjweaver-1.9.24.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.3.232/h2-2.3.232.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-websocket/3.5.2/spring-boot-starter-websocket-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/6.2.8/spring-messaging-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-websocket/6.2.8/spring-websocket-6.2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.5.2/spring-boot-starter-thymeleaf-3.5.2.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.3.RELEASE/thymeleaf-spring6-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.3.RELEASE/thymeleaf-3.1.3.RELEASE.jar:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.5.2/spring-boot-starter-validation-3.5.2.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.42/tomcat-embed-el-10.1.42.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.2.Final/hibernate-validator-8.0.2.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-ui/2.2.0/springdoc-openapi-starter-webmvc-ui-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-webmvc-api/2.2.0/springdoc-openapi-starter-webmvc-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-starter-common/2.2.0/springdoc-openapi-starter-common-2.2.0.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core-jakarta/2.2.15/swagger-core-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations-jakarta/2.2.15/swagger-annotations-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models-jakarta/2.2.15/swagger-models-jakarta-2.2.15.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/5.2.0/swagger-ui-5.2.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-openai/1.0.0/spring-ai-openai-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-model/1.0.0/spring-ai-model-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-commons/1.0.0/spring-ai-commons-1.0.0.jar:/Users/<USER>/.m2/repository/io/micrometer/context-propagation/1.1.3/context-propagation-1.1.3.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-template-st/1.0.0/spring-ai-template-st-1.0.0.jar:/Users/<USER>/.m2/repository/org/antlr/ST4/4.3.4/ST4-4.3.4.jar:/Users/<USER>/.m2/repository/org/antlr/antlr-runtime/3.5.3/antlr-runtime-3.5.3.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-retry/1.0.0/spring-ai-retry-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/retry/spring-retry/2.0.12/spring-retry-2.0.12.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webflux/6.2.8/spring-webflux-6.2.8.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-generator/4.37.0/jsonschema-generator-4.37.0.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-jackson/4.37.0/jsonschema-module-jackson-4.37.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/6.2.8/spring-context-support-6.2.8.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.17/slf4j-api-2.0.17.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-starter-model-openai/1.0.0/spring-ai-starter-model-openai-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-openai/1.0.0/spring-ai-autoconfigure-model-openai-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-tool/1.0.0/spring-ai-autoconfigure-model-tool-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-retry/1.0.0/spring-ai-autoconfigure-retry-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-chat-observation/1.0.0/spring-ai-autoconfigure-model-chat-observation-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-embedding-observation/1.0.0/spring-ai-autoconfigure-model-embedding-observation-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-image-observation/1.0.0/spring-ai-autoconfigure-model-image-observation-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-chat-memory/1.0.0/spring-ai-autoconfigure-model-chat-memory-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-client-chat/1.0.0/spring-ai-client-chat-1.0.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jsonSchema/2.19.1/jackson-module-jsonSchema-2.19.1.jar:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.25/swagger-annotations-2.2.25.jar:/Users/<USER>/.m2/repository/com/github/victools/jsonschema-module-swagger-2/4.37.0/jsonschema-module-swagger-2-4.37.0.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.7.7/reactor-core-3.7.7.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/6.2.8/spring-context-6.2.8.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/1.1.0/jtokkit-1.1.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-mcp/1.0.0/spring-ai-mcp-1.0.0.jar:/Users/<USER>/.m2/repository/io/modelcontextprotocol/sdk/mcp/0.10.0/mcp-0.10.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-starter-mcp-server-webmvc/1.0.0/spring-ai-starter-mcp-server-webmvc-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-mcp-server/1.0.0/spring-ai-autoconfigure-mcp-server-1.0.0.jar:/Users/<USER>/.m2/repository/io/modelcontextprotocol/sdk/mcp-spring-webmvc/0.10.0/mcp-spring-webmvc-0.10.0.jar:/Users/<USER>/.m2/repository/org/springframework/ai/spring-ai-autoconfigure-model-chat-client/1.0.0/spring-ai-autoconfigure-model-chat-client-1.0.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/3.5.2/spring-boot-starter-actuator-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/3.5.2/spring-boot-actuator-autoconfigure-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/3.5.2/spring-boot-actuator-3.5.2.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.15.1/micrometer-observation-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.15.1/micrometer-commons-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-jakarta9/1.15.1/micrometer-jakarta9-1.15.1.jar:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.15.1/micrometer-core-1.15.1.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar:/Users/<USER>/.m2/repository/info/picocli/picocli/4.7.7/picocli-4.7.7.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/5.4.1/poi-ooxml-5.4.1.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/5.4.1/poi-5.4.1.jar:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.3/SparseBitSet-1.3.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-lite/5.4.1/poi-ooxml-lite-5.4.1.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/5.3.0/xmlbeans-5.3.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.27.1/commons-compress-1.27.1.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.18.0/commons-io-2.18.0.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.08/curvesapi-1.08.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.24.3/log4j-api-2.24.3.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-scratchpad/5.4.1/poi-scratchpad-5.4.1.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox/3.0.5/pdfbox-3.0.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-io/3.0.5/pdfbox-io-3.0.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/fontbox/3.0.5/fontbox-3.0.5.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.3.5/commons-logging-1.3.5.jar:/Users/<USER>/.m2/repository/org/jsoup/jsoup/1.20.1/jsoup-1.20.1.jar:/Users/<USER>/.m2/repository/net/sourceforge/tess4j/tess4j/5.16.0/tess4j-5.16.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.17.0/jna-5.17.0.jar:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-tools/3.0.5/pdfbox-tools-3.0.5.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/pdfbox-debugger/3.0.5/pdfbox-debugger-3.0.5.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcpkix-jdk18on/1.80/bcpkix-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcutil-jdk18on/1.80/bcutil-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk18on/1.80/bcprov-jdk18on-1.80.jar:/Users/<USER>/.m2/repository/org/apache/pdfbox/jbig2-imageio/3.0.4/jbig2-imageio-3.0.4.jar:/Users/<USER>/.m2/repository/net/sourceforge/lept4j/lept4j/1.21.1/lept4j-1.21.1.jar:/Users/<USER>/.m2/repository/org/jboss/jboss-vfs/3.2.17.Final/jboss-vfs-3.2.17.Final.jar:/Users/<USER>/.m2/repository/org/odftoolkit/odfdom-java/0.12.0/odfdom-java-0.12.0.jar:/Users/<USER>/.m2/repository/xerces/xercesImpl/2.12.2/xercesImpl-2.12.2.jar:/Users/<USER>/.m2/repository/xalan/serializer/2.7.3/serializer-2.7.3.jar:/Users/<USER>/.m2/repository/org/apache/jena/jena-core/4.10.0/jena-core-4.10.0.jar:/Users/<USER>/.m2/repository/org/apache/jena/jena-base/4.10.0/jena-base-4.10.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.10.0/commons-csv-1.10.0.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/3.2.1/caffeine-3.2.1.jar:/Users/<USER>/.m2/repository/org/jspecify/jspecify/1.0.0/jspecify-1.0.0.jar:/Users/<USER>/.m2/repository/com/github/andrewoma/dexx/collection/0.7/collection-0.7.jar:/Users/<USER>/.m2/repository/org/apache/jena/jena-iri/4.10.0/jena-iri-4.10.0.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.5.0/commons-cli-1.5.0.jar:/Users/<USER>/.m2/repository/org/roaringbitmap/RoaringBitmap/1.0.0/RoaringBitmap-1.0.0.jar:/Users/<USER>/.m2/repository/net/rootdev/java-rdfa/1.0.0-BETA1/java-rdfa-1.0.0-BETA1.jar:/Users/<USER>/.m2/repository/commons-validator/commons-validator/1.7/commons-validator-1.7.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/Users/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar:/Users/<USER>/.m2/repository/org/json/json/20231013/json-20231013.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/2.0.17/slf4j-simple-2.0.17.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.19.1/jackson-databind-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.19.1/jackson-annotations-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.19.1/jackson-core-2.19.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.19.1/jackson-dataformat-yaml-2.19.1.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.2-jre/guava-32.1.2-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.33.0/checker-qual-3.33.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.18.0/error_prone_annotations-2.18.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/2.8/j2objc-annotations-2.8.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.5.2/spring-boot-starter-test-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.5.2/spring-boot-test-3.5.2.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.5.2/spring-boot-test-autoconfigure-3.5.2.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.9.0/json-path-2.9.0.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.5.2/json-smart-2.5.2.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.2/accessors-smart-2.5.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.7.1/asm-9.7.1.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.27.3/assertj-core-3.27.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.2/awaitility-4.2.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/3.0/hamcrest-3.0.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.12.2/junit-jupiter-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.12.2/junit-jupiter-api-5.12.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.12.2/junit-platform-commons-1.12.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.12.2/junit-jupiter-params-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.12.2/junit-jupiter-engine-5.12.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.12.2/junit-platform-engine-1.12.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/5.17.0/mockito-core-5.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.17.6/byte-buddy-agent-1.17.6.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.17.0/mockito-junit-jupiter-5.17.0.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.3/jsonassert-1.5.3.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/6.2.8/spring-test-6.2.8.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.10.2/xmlunit-core-2.10.2.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-03-18"/>
    <property name="java.home" value="/Users/<USER>/.sdkman/candidates/java/24-graal"/>
    <property name="spring.profiles.active" value="test"/>
    <property name="file.separator" value="/"/>
    <property name="https.proxyHost" value="127.0.0.1"/>
    <property name="basedir" value="/Users/<USER>/Downloads/重庆园区/src/search/converter"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="jdk.internal.vm.ci.enabled" value="true"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Downloads/重庆园区/src/search/converter/target/surefire/surefirebooter-20250630011147690_620.jar"/>
    <property name="junit.platform.listeners.uid.tracking.enabled" value="true"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="jdk.instrument.traceUsage" value="false"/>
    <property name="junit.platform.listeners.uid.tracking.output.dir" value="/Users/<USER>/Downloads/重庆园区/src/search/converter/target/test-ids"/>
    <property name="java.runtime.version" value="24+36-jvmci-b01"/>
    <property name="user.name" value="lzq"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Oracle GraalVM 24+36.1"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/lc/9bx_gj5s1jv4fcj183zms9jc0000gn/T/"/>
    <property name="java.version" value="24"/>
    <property name="user.dir" value="/Users/<USER>/Downloads/重庆园区/src/search/converter"/>
    <property name="os.arch" value="aarch64"/>
    <property name="socksProxyPort" value="1080"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="24+36-jvmci-b01"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="java.class.version" value="68.0"/>
    <property name="http.proxyPort" value="1083"/>
  </properties>
  <testcase name="testConvertSingleFile" classname="com.talkweb.ai.converter.cli.ConvertCommandTest" time="1.233">
    <system-out><![CDATA[&amp#27;[0;36m[INFO]&amp#27;[0m Processing file: /var/folders/lc/9bx_gj5s1jv4fcj183zms9jc0000gn/T/junit-3014874960706882421/test.txt
&amp#27;[0;32m[SUCCESS]&amp#27;[0m Conversion completed successfully!
]]></system-out>
    <system-err><![CDATA[&amp#27;[0;31m[ERROR]&amp#27;[0m Failed to generate conversion report: No serializer found for class com.talkweb.ai.converter.cli.ConvertCommand$ConversionReport and no properties discovered to create BeanSerializer (to avoid exception, disable SerializationFeature.FAIL_ON_EMPTY_BEANS)
]]></system-err>
  </testcase>
  <testcase name="testConvertDirectory" classname="com.talkweb.ai.converter.cli.ConvertCommandTest" time="1.039">
    <system-out><![CDATA[&amp#27;[0;36m[INFO]&amp#27;[0m Scanning directory: /var/folders/lc/9bx_gj5s1jv4fcj183zms9jc0000gn/T/junit-18238076760550838066/testDir
&amp#27;[0;36m[INFO]&amp#27;[0m Found 2 files to process

Converting: [>                                                 ] 0.0% (0/2) 00:00
Converting: [=========================>                        ] 50.0% (1/2) 00:00
Converting: [==================================================] 100.0% (2/2) 00:01
&amp#27;[0;36m[INFO]&amp#27;[0m Processed 2 files: 2 succeeded, 0 failed
&amp#27;[0;32m[SUCCESS]&amp#27;[0m Conversion completed successfully!
]]></system-out>
    <system-err><![CDATA[&amp#27;[0;31m[ERROR]&amp#27;[0m Failed to generate conversion report: No serializer found for class com.talkweb.ai.converter.cli.ConvertCommand$ConversionReport and no properties discovered to create BeanSerializer (to avoid exception, disable SerializationFeature.FAIL_ON_EMPTY_BEANS)
]]></system-err>
  </testcase>
</testsuite>